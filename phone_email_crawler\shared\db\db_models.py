# -*- coding: utf-8 -*-
"""
Database models for phone and email crawler
Author: <PERSON><PERSON> <PERSON>i
Create Time: 2025/8/18
File Name: db_models.py
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
# 注意：移除了 ForeignKey 和 relationship 导入，因为不再使用外键约束

Base = declarative_base()


class SearchTasks(Base):
    """搜索任务表"""
    __tablename__ = 'search_tasks'

    id = Column(Integer, primary_key=True, autoincrement=True)
    keyword = Column(String(255), nullable=False, comment='关键词，如：hotel，来源于ebay/关键字.txt')
    region = Column(String(255), nullable=False, comment='地区，如：ny，来源于ebay/county_list.json')
    search_phone_status = Column(String(50), default='pending', comment='搜索电话任务状态：pending/completed')
    search_email_status = Column(String(50), default='pending', comment='搜索邮箱任务状态：pending/completed（已废弃，使用phone_results.email_search_status）')
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())

    # 添加索引提高查询性能
    __table_args__ = (
        Index('idx_phone_status', 'search_phone_status'),
        Index('idx_email_status', 'search_email_status'),
        Index('idx_keyword_region', 'keyword', 'region'),
    )

    # 注意：移除了 relationship 定义，因为不再使用外键约束
    # 如需查询关联数据，请使用手动 JOIN 查询


class PhoneResults(Base):
    """电话搜索结果表"""
    __tablename__ = 'phone_results'

    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(Integer, nullable=False, comment='关联search_tasks.id（无外键约束）')
    keyword = Column(String(255), nullable=False, comment='来自search_tasks的keyword')
    region = Column(String(255), nullable=False, comment='来自search_tasks的region')
    search_query = Column(Text, nullable=False, comment='实际搜索的查询词')
    url = Column(Text, comment='搜索结果URL')
    phone_number = Column(String(50), comment='提取的电话号码')
    current_page = Column(Integer, nullable=False, comment='搜索结果的当前页码')
    email_search_status = Column(String(50), default='pending', comment='邮箱搜索状态：pending/completed')
    created_at = Column(DateTime, default=func.current_timestamp())

    # 添加索引
    __table_args__ = (
        Index('idx_task_id', 'task_id'),
        Index('idx_phone_number', 'phone_number'),
        Index('idx_keyword_region', 'keyword', 'region'),
    )

    # 注意：移除了 relationship 定义，因为不再使用外键约束
    # 如需查询关联数据，请使用手动 JOIN 查询


class EmailResults(Base):
    """邮箱搜索结果表"""
    __tablename__ = 'email_results'

    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(Integer, nullable=False, comment='关联search_tasks.id（无外键约束）')
    phone_result_id = Column(Integer, nullable=False, comment='关联phone_results.id（无外键约束）')
    keyword = Column(String(255), nullable=False, comment='来自search_tasks的keyword')
    region = Column(String(255), nullable=False, comment='来自search_tasks的region')
    phone_number = Column(String(50), nullable=False, comment='来自phone_results的phone_number')
    search_query = Column(Text, nullable=False, comment='实际搜索的查询词，即电话号码+email')
    url = Column(Text, comment='搜索结果URL')
    email_address = Column(String(255), comment='提取的邮箱地址')
    current_page = Column(Integer, nullable=False, comment='搜索结果的当前页码')
    created_at = Column(DateTime, default=func.current_timestamp())

    # 添加索引
    __table_args__ = (
        Index('idx_task_id', 'task_id'),
        Index('idx_phone_result_id', 'phone_result_id'),
        Index('idx_phone_number', 'phone_number'),
        Index('idx_email_address', 'email_address'),
    )

    # 注意：移除了 relationship 定义，因为不再使用外键约束
    # 如需查询关联数据，请使用手动 JOIN 查询
