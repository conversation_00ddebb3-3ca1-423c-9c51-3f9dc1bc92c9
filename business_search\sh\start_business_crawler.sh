#!/bin/bash

# Business Crawler Starter
# Author: ji<PERSON> <PERSON>i
# Create Time: 2025/9/3
# File Name: start_business_crawler.sh
# Description: Starts business search crawler with cycle management

# Configuration
PROJECT_DIR="/root/google-search/business_search"
BUSINESS_SCRIPT="$PROJECT_DIR/main.py"
CLEANUP_SCRIPT="$PROJECT_DIR/sh/cleanup_browsers.sh"
RUN_DURATION=1800  # 30 minutes in seconds (30 * 60)
BREAK_DURATION=60  # 1 minute in seconds
LOG_FILE="$PROJECT_DIR/logs/business_cycle.log"
PID_FILE="$PROJECT_DIR/logs/business_crawler.pid"

# Create logs directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"
mkdir -p "$(dirname "$PID_FILE")"

# Function to log messages with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to cleanup and exit gracefully
cleanup_and_exit() {
    log_message "收到停止信号，正在清理业务爬虫..."
    
    # Kill business crawler if it's running
    if [ ! -z "$BUSINESS_PID" ] && kill -0 "$BUSINESS_PID" 2>/dev/null; then
        log_message "停止业务爬虫 (PID: $BUSINESS_PID)"
        kill -TERM "$BUSINESS_PID" 2>/dev/null
        sleep 3
        # Force kill if still running
        if kill -0 "$BUSINESS_PID" 2>/dev/null; then
            kill -KILL "$BUSINESS_PID" 2>/dev/null
        fi
    fi
    
    # Run browser cleanup
    if [ -f "$CLEANUP_SCRIPT" ]; then
        log_message " 清理浏览器进程..."
        bash "$CLEANUP_SCRIPT"
    fi
    
    # Remove PID file
    rm -f "$PID_FILE"
    
    log_message "业务爬虫清理完成，程序退出"
    exit 0
}

# Check if already running
if [ -f "$PID_FILE" ]; then
    OLD_PID=$(cat "$PID_FILE")
    if kill -0 "$OLD_PID" 2>/dev/null; then
        echo "业务爬虫已在运行 (PID: $OLD_PID)"
        echo "请先运行 stop_business_crawler.sh 停止现有进程"
        exit 1
    else
        rm -f "$PID_FILE"
    fi
fi

# Save current PID
echo $$ > "$PID_FILE"

# Set up signal handlers
trap cleanup_and_exit SIGINT SIGTERM

# Main cycle loop
cycle_count=0

log_message "启动业务爬虫循环脚本"
log_message "配置: 运行时间=${RUN_DURATION}秒(30分钟), 休息时间=${BREAK_DURATION}秒(1分钟)"
log_message "业务爬虫: $BUSINESS_SCRIPT"
log_message "清理脚本: $CLEANUP_SCRIPT"

while true; do
    cycle_count=$((cycle_count + 1))
    log_message "===== 业务爬虫第 $cycle_count 个循环 ====="
    
    # Step 1: Browser cleanup
    log_message "步骤1: 清理浏览器缓存..."
    if [ -f "$CLEANUP_SCRIPT" ]; then
        bash "$CLEANUP_SCRIPT"
        log_message "浏览器清理完成"
    else
        log_message "清理脚本不存在: $CLEANUP_SCRIPT"
    fi
    
    # Step 2: Run business crawler for 30 minutes
    log_message "步骤2: 启动业务爬虫，运行30分钟..."
    
    if [ -f "$BUSINESS_SCRIPT" ]; then
        # Start business crawler in background
        cd "/root/google-search"
        export PYTHONPATH="/root/google-search:$PYTHONPATH"
        python3 "$BUSINESS_SCRIPT" &
        BUSINESS_PID=$!

        log_message "▶业务爬虫已启动 (PID: $BUSINESS_PID)"
        log_message "📝 可通过以下命令查看进程: ps aux | grep main.py"
        
        # Wait for 30 minutes or until process ends
        start_time=$(date +%s)
        while [ $(($(date +%s) - start_time)) -lt $RUN_DURATION ]; do
            # Check if process is still running
            if ! kill -0 "$BUSINESS_PID" 2>/dev/null; then
                log_message "业务爬虫提前结束"
                break
            fi
            sleep 10  # Check every 10 seconds
        done
        
        # Stop business crawler if still running
        if kill -0 "$BUSINESS_PID" 2>/dev/null; then
            log_message "30分钟到达，停止业务爬虫"
            kill -TERM "$BUSINESS_PID" 2>/dev/null
            sleep 5
            # Force kill if still running
            if kill -0 "$BUSINESS_PID" 2>/dev/null; then
                log_message "强制停止业务爬虫"
                kill -KILL "$BUSINESS_PID" 2>/dev/null
            fi
        fi
        
        log_message "业务爬虫运行周期完成"
    else
        log_message "业务爬虫不存在: $BUSINESS_SCRIPT"
        log_message "等待30分钟后继续..."
        sleep $RUN_DURATION
    fi
    
    # Step 3: Break for 1 minute
    log_message "步骤3: 休息1分钟..."
    sleep $BREAK_DURATION
    log_message "休息结束"
    
    log_message "===== 业务爬虫第 $cycle_count 个循环完成 ====="
    echo ""  # Add empty line for readability
done
