# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>reate Time: 2025/3/19 17:18
File Name: db_models.py
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class SearchQueries(Base):
    __tablename__ = 'search_aol_email'

    id = Column(Integer, primary_key=True, autoincrement=True)
    county = Column(String(1024), nullable=False)
    goods = Column(String(1024), nullable=False)
    email_type = Column(String(255), nullable=False)
    # crawled_status为枚举类型，只能是pending、completed
    crawled_status = Column(String(255))
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())


class SearchResults(Base):
    __tablename__ = 'search_results'
    id = Column(Integer, primary_key=True, autoincrement=True)
    url = Column(Text)
    email = Column(Text)
    current_page = Column(Integer, nullable=False)
    city = Column(String(100), nullable=False)
    goods = Column(String(100), nullable=False)
    email_type = Column(String(50), nullable=False)
    date = Column(DateTime, nullable=False)
