# -*- coding: utf-8 -*-
"""
邮箱数据删除工具 (优化版 v5 - 增加删除安全条件)
Author: ji<PERSON> wei
Create Time: 2025/8/18
File Name: delete_email_qt_optimized_v5.py
"""
import sys
import time
from datetime import datetime

import pymysql
from PyQt5.QtCore import QDateTime, QThread, pyqtSignal
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (QApplication, QButtonGroup, QDateTimeEdit,
                             QDesktopWidget, QGroupBox, QHBoxLayout, QLabel,
                             QMainWindow, QMessageBox, QProgressBar,
                             QPushButton, QRadioButton, QSpinBox, QTextEdit,
                             QVBoxLayout, QWidget)
# 数据库连接配置
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 数据库配置
MYSQL_HOST = '*************'
MYSQL_PORT = '13306'
MYSQL_USER = 'root'
MYSQL_PASSWORD = 'qwer1234'
MYSQL_DATABASE = 'phone_email'

# 创建数据库连接
DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}?charset=utf8mb4"

engine = create_engine(
    DATABASE_URL,
    pool_recycle=3600,
    pool_pre_ping=True,
    pool_size=10,
    max_overflow=20,
    echo=False,
    connect_args={
        'connect_timeout': 60,
        'read_timeout': 300,
        'write_timeout': 300,
    }
)

# 创建Session工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db_session():
    """获取数据库会话"""
    return SessionLocal()


class EmailDeleteWorker(QThread):
    """删除邮箱数据的工作线程 (优化版 v5)"""
    progress_updated = pyqtSignal(int, int, str, str)  # 已删除数量, 总数量, 剩余时间, 速度信息
    finished = pyqtSignal(str)
    error = pyqtSignal(str)
    status_updated = pyqtSignal(str)

    def __init__(self, delete_type, id_threshold=None, datetime_threshold=None):
        super().__init__()
        self.delete_type = delete_type
        self.id_threshold = id_threshold
        self.datetime_threshold = datetime_threshold
        self.batch_size = 10000  # 每批次处理的记录数
        self.is_running = True

    def get_total_count(self):
        """获取要删除的总记录数"""
        db = None
        try:
            db = get_db_session()
            if self.delete_type == 'id':
                query = text("SELECT COUNT(*) FROM email_results WHERE id <= :id_threshold")
                params = {"id_threshold": self.id_threshold}
            else:  # datetime
                query = text("SELECT COUNT(*) FROM email_results WHERE created_at <= :datetime_threshold")
                params = {"datetime_threshold": self.datetime_threshold}

            result = db.execute(query, params)
            count = result.scalar_one_or_none() or 0
            return count
        except Exception as e:
            self.error.emit(f"获取邮箱记录数失败: {str(e)}")
            return 0
        finally:
            if db:
                db.close()

    def delete_email_batch(self):
        """删除一个批次的 email_results 记录"""
        db = None
        try:
            db = get_db_session()
            if self.delete_type == 'id':
                query = text("""
                    SELECT id FROM email_results
                    WHERE id <= :id_threshold
                    ORDER BY id
                    LIMIT :batch_size
                """)
                params = {"id_threshold": self.id_threshold, "batch_size": self.batch_size}
            else:
                query = text("""
                    SELECT id FROM email_results
                    WHERE created_at <= :datetime_threshold
                    ORDER BY id
                    LIMIT :batch_size
                """)
                params = {"datetime_threshold": self.datetime_threshold, "batch_size": self.batch_size}

            result = db.execute(query, params)
            email_ids = [row[0] for row in result.fetchall()]

            if not email_ids:
                return 0

            ids_str = ','.join(map(str, email_ids))
            delete_query = text(f"DELETE FROM email_results WHERE id IN ({ids_str})")
            delete_result = db.execute(delete_query)

            db.commit()
            return delete_result.rowcount

        except Exception as e:
            if db:
                db.rollback()
            raise e
        finally:
            if db:
                db.close()

    def cleanup_orphaned_phones(self):
        """
        第二阶段：高效、分批次地清理孤立的 phone_results 记录。
        【核心修正】增加安全条件：只删除 email_search_status = 'completed' 的记录。
        """
        self.status_updated.emit("🧹 [阶段二] 开始清理孤立且已完成的电话记录...")
        total_cleaned_up = 0

        while self.is_running:
            db = None
            try:
                db = get_db_session()

                # 步骤1: 查找一批孤立且状态为 'completed' 的电话记录ID
                find_orphans_query = text(f"""
                    SELECT p.id
                    FROM phone_results AS p
                    LEFT JOIN email_results AS e ON p.id = e.phone_result_id
                    WHERE e.id IS NULL AND p.email_search_status = 'completed'
                    LIMIT {self.batch_size}
                """)
                result = db.execute(find_orphans_query)
                phone_ids_to_delete = [row[0] for row in result.fetchall()]

                if not phone_ids_to_delete:
                    self.status_updated.emit("✅ 所有符合条件的孤立电话记录清理完毕。")
                    break

                # 步骤2: 删除找到的ID
                ids_str = ','.join(map(str, phone_ids_to_delete))
                delete_query = text(f"DELETE FROM phone_results WHERE id IN ({ids_str})")

                delete_result = db.execute(delete_query)
                deleted_in_batch = delete_result.rowcount
                db.commit()

                total_cleaned_up += deleted_in_batch
                self.status_updated.emit(f"🧹 已清理 {total_cleaned_up:,} 条孤立电话记录...")

            except Exception as e:
                if db:
                    db.rollback()
                self.error.emit(f"清理孤立电话记录失败: {str(e)}")
                return total_cleaned_up
            finally:
                if db:
                    db.close()

        return total_cleaned_up

    def run(self):
        """执行删除操作"""
        self.status_updated.emit("📊 正在统计符合条件的记录数...")
        total_to_delete = self.get_total_count()

        deleted_count = 0
        start_time = time.time()

        # --- 阶段一：删除 email_results ---
        if total_to_delete > 0:
            self.status_updated.emit(f"📧 [阶段一] 发现 {total_to_delete:,} 条邮箱记录，开始删除...")
            try:
                while self.is_running and deleted_count < total_to_delete:
                    deleted_in_batch = 0
                    for attempt in range(3):
                        try:
                            deleted_in_batch = self.delete_email_batch()
                            break
                        except (pymysql.err.OperationalError, Exception) as e:
                            if attempt < 2:
                                self.status_updated.emit(f"⚠️ 数据库操作失败，2秒后重试... ({str(e)[:80]})")
                                time.sleep(2)
                            else:
                                raise e

                    if deleted_in_batch == 0:
                        break

                    deleted_count += deleted_in_batch

                    elapsed_time = time.time() - start_time
                    if deleted_count > 0:
                        speed = deleted_count / elapsed_time
                        remaining_records = total_to_delete - deleted_count
                        remaining_time = remaining_records / speed if speed > 0 else 0
                        remaining_time_str = self.format_time(remaining_time)
                        speed_str = f"{speed / 1000:.1f}K条/秒" if speed >= 1000 else f"{speed:.1f}条/秒"
                    else:
                        remaining_time_str = "计算中..."
                        speed_str = "计算中..."

                    self.progress_updated.emit(deleted_count, total_to_delete, remaining_time_str, speed_str)
                    time.sleep(0.01)

            except Exception as e:
                self.error.emit(f"删除邮箱记录过程中出错: {str(e)}")
                return
        else:
            self.status_updated.emit("📧 [阶段一] 没有找到符合条件的邮箱记录，直接进入下一阶段。")

        # --- 阶段二：清理孤立的 phone_results ---
        total_phone_deleted = 0
        if self.is_running:
            total_phone_deleted = self.cleanup_orphaned_phones()

        # --- 生成最终报告 ---
        final_elapsed_time = time.time() - start_time

        if self.is_running:
            if deleted_count > 0:
                avg_speed = deleted_count / final_elapsed_time
                avg_speed_str = f"{avg_speed / 1000:.1f}K条/秒" if avg_speed >= 1000 else f"{avg_speed:.1f}条/秒"
                summary = (
                    f"🎯 删除完成！\n\n"
                    f"📧 删除邮箱记录: {deleted_count:,} 条\n"
                    f"📞 清理电话记录: {total_phone_deleted:,} 条\n"
                    f"⏱️ 总耗时: {self.format_time(final_elapsed_time)}\n"
                    f"🚀 平均速度: {avg_speed_str}"
                )
            else:
                summary = (
                    f"🎯 操作完成！\n\n"
                    f"📧 未删除任何邮箱记录。\n"
                    f"📞 清理孤立电话记录: {total_phone_deleted:,} 条\n"
                    f"⏱️ 总耗时: {self.format_time(final_elapsed_time)}"
                )
            self.finished.emit(summary)
        else:
            summary = (
                f"🛑 删除已手动停止！\n\n"
                f"📧 已删除邮箱记录: {deleted_count:,} 条\n"
                f"📞 已清理电话记录: {total_phone_deleted:,} 条\n"
                f"⏱️ 耗时: {self.format_time(final_elapsed_time)}"
            )
            self.finished.emit(summary)

    def format_time(self, seconds):
        """格式化时间显示"""
        seconds = int(seconds)
        if seconds < 60:
            return f"{seconds}秒"
        elif seconds < 3600:
            minutes, secs = divmod(seconds, 60)
            return f"{minutes}分{secs}秒"
        else:
            hours, remainder = divmod(seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            return f"{hours}小时{minutes}分钟"

    def stop(self):
        """停止删除操作"""
        self.is_running = False
        self.status_updated.emit("⏳ 正在请求停止...将在当前批次完成后安全退出。")


class EmailDeleteMainWindow(QMainWindow):
    """邮箱删除工具主窗口类"""

    def __init__(self):
        super().__init__()
        self.worker = None
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("邮箱数据删除工具 (优化版 v5)")
        self.resize(800, 600)
        self.center_window()

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # --- 删除条件组 ---
        condition_group = QGroupBox("删除条件")
        condition_layout = QVBoxLayout(condition_group)

        self.delete_type_group = QButtonGroup()
        self.id_radio = QRadioButton("按邮箱ID删除 (ID小于等于)")
        self.id_radio.setChecked(True)
        self.delete_type_group.addButton(self.id_radio)

        id_layout = QHBoxLayout()
        self.id_spinbox = QSpinBox()
        self.id_spinbox.setRange(1, 2000000000)
        self.id_spinbox.setValue(10000)
        id_layout.addWidget(self.id_radio)
        id_layout.addWidget(self.id_spinbox)
        id_layout.addStretch()
        condition_layout.addLayout(id_layout)

        self.datetime_radio = QRadioButton("按创建时间删除 (时间早于等于)")
        self.delete_type_group.addButton(self.datetime_radio)

        datetime_layout = QHBoxLayout()
        self.datetime_edit = QDateTimeEdit()
        self.datetime_edit.setDateTime(QDateTime.currentDateTime())
        self.datetime_edit.setDisplayFormat("yyyy-MM-dd hh:mm:ss")
        self.datetime_edit.setCalendarPopup(True)
        datetime_layout.addWidget(self.datetime_radio)
        datetime_layout.addWidget(self.datetime_edit)
        datetime_layout.addStretch()
        condition_layout.addLayout(datetime_layout)

        info_label = QLabel(
            "⚠️ 注意：删除操作分两步，先删除所有符合条件的邮箱，然后统一清理不再被任何邮箱引用的、且搜索状态为'completed'的电话记录。\n"
            "🚀 此方法大幅提升了对海量数据的删除性能。"
        )
        info_label.setStyleSheet("color: #FF6B35; font-weight: bold; padding: 5px;")
        info_label.setWordWrap(True)
        condition_layout.addWidget(info_label)

        main_layout.addWidget(condition_group)

        # --- 进度显示组 ---
        progress_group = QGroupBox("删除进度")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("进度: 0/0 (0%)")
        self.time_label = QLabel("预计剩余时间: --")
        self.speed_label = QLabel("处理速度: --")

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.progress_label)
        progress_layout.addWidget(self.time_label)
        progress_layout.addWidget(self.speed_label)
        main_layout.addWidget(progress_group)

        # --- 控制按钮 ---
        button_layout = QHBoxLayout()
        self.start_button = QPushButton("开始删除")
        self.start_button.clicked.connect(self.start_delete)
        self.stop_button = QPushButton("停止")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_delete)
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        main_layout.addLayout(button_layout)

        # --- 日志显示 ---
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        main_layout.addWidget(log_group)

        font = QFont("Microsoft YaHei", 9)
        self.setFont(font)
        self.add_log("📋 邮箱删除工具已启动，等待操作...")

    def center_window(self):
        """将窗口居中显示"""
        screen_geo = QDesktopWidget().screenGeometry()
        self_geo = self.geometry()
        self.move((screen_geo.width() - self_geo.width()) // 2,
                  (screen_geo.height() - self_geo.height()) // 2)

    def start_delete(self):
        """开始删除操作"""
        if self.id_radio.isChecked():
            delete_type = 'id'
            id_threshold = self.id_spinbox.value()
            datetime_threshold = None
            condition_desc = f"邮箱ID小于等于 {id_threshold}"
        else:
            delete_type = 'datetime'
            datetime_threshold = self.datetime_edit.dateTime().toString("yyyy-MM-dd hh:mm:ss")
            id_threshold = None
            condition_desc = f"创建时间早于等于 {datetime_threshold}"

        reply = QMessageBox.question(
            self, "确认删除",
            f"您确定要删除所有符合条件的邮箱记录吗？\n\n删除条件: {condition_desc}\n\n此操作不可撤销，请谨慎操作！",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        self.worker = EmailDeleteWorker(delete_type, id_threshold, datetime_threshold)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.finished.connect(self.delete_finished)
        self.worker.error.connect(self.delete_error)
        self.worker.status_updated.connect(self.add_log)

        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)
        self.log_text.clear()

        self.add_log(f"🚀 开始删除操作...")
        self.add_log(f"📋 删除条件: {condition_desc}")
        self.add_log(f"📦 批次大小: {self.worker.batch_size:,}")

        self.worker.start()

    def stop_delete(self):
        """停止删除操作"""
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.stop_button.setEnabled(False)

    def update_progress(self, deleted_count, total_count, remaining_time, speed_info):
        """更新进度显示"""
        if total_count > 0:
            progress = int((deleted_count / total_count) * 100)
            self.progress_bar.setValue(progress)
            self.progress_label.setText(f"进度: {deleted_count:,}/{total_count:,} ({progress}%)")
        else:
            self.progress_label.setText(f"进度: {deleted_count:,}/?")

        self.time_label.setText(f"预计剩余时间: {remaining_time}")
        self.speed_label.setText(f"处理速度: {speed_info}")

    def delete_finished(self, message):
        """删除完成"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.add_log(message)
        self.progress_bar.setValue(100)
        QMessageBox.information(self, "操作完成", message)

    def delete_error(self, error_message):
        """删除出错"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.add_log(f"❌ 错误: {error_message}")
        QMessageBox.critical(self, "发生错误", error_message)

    def add_log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())


def main():
    app = QApplication(sys.argv)
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    window = EmailDeleteMainWindow()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
