# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON>i
<PERSON>reate Time: 2025/9/1
File Name: db_models.py
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, func
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class SearchQueries(Base):
    """搜索查询表"""
    __tablename__ = 'business'

    id = Column(Integer, primary_key=True, autoincrement=True)
    business = Column(String(1024), nullable=False)
    # crawled_status为枚举类型，只能是pending、completed
    crawled_status = Column(String(255), default='pending')
    created_at = Column(DateTime, default=func.current_timestamp())

    def __repr__(self):
        return f"<SearchQueries(id={self.id}, business='{self.business}', status='{self.crawled_status}')>"


class SearchResults(Base):
    """搜索结果表"""
    __tablename__ = 'search_results'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    url = Column(Text)
    email = Column(Text)
    current_page = Column(Integer, nullable=False)
    business = Column(String(1024), nullable=False)
    date = Column(DateTime, default=func.current_timestamp())

    def __repr__(self):
        return f"<SearchResults(id={self.id}, business='{self.business}', email='{self.email}')>"
