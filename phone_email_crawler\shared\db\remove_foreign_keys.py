# -*- coding: utf-8 -*-
"""
删除数据库外键约束的脚本 - 大数据量优化版本
注意：此脚本只删除外键约束，不删除数据和列
适用于百万级数据量的高性能处理
Author: AI Assistant
Create Time: 2025/9/8
File Name: remove_foreign_keys.py
"""

from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from dotenv import load_dotenv

load_dotenv()

# 数据库配置
mysql_host = os.getenv('MYSQL_HOST', '************')
mysql_port = os.getenv('MYSQL_PORT', '13306')
mysql_user = os.getenv('MYSQL_USER', 'root')
mysql_password = os.getenv('MYSQL_PASSWORD', 'qwer1234')
mysql_db = os.getenv('MYSQL_DATABASE', 'phone_email')

# 创建数据库连接 - 大数据量优化配置
DATABASE_URL = f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_db}"

engine = create_engine(
    DATABASE_URL,
    pool_recycle=3600,      # 增加连接回收时间
    pool_pre_ping=True,
    pool_size=20,           # 增加连接池大小
    max_overflow=30,        # 增加最大溢出连接
    echo=False,             # 关闭SQL日志以提高性能
    connect_args={
        'connect_timeout': 60,      # 连接超时
        'read_timeout': 300,        # 读取超时
        'write_timeout': 300,       # 写入超时
        'charset': 'utf8mb4'
    }
)


def print_progress_bar(current, total, prefix='进度', suffix='完成', length=50):
    """显示进度条"""
    percent = (current / total) * 100
    filled_length = int(length * current // total)
    bar = '█' * filled_length + '-' * (length - filled_length)
    print(f'\r{prefix} |{bar}| {current}/{total} ({percent:.1f}%) {suffix}', end='', flush=True)


def get_table_info_fast(table_name):
    """快速获取表信息（不扫描全表）"""
    try:
        with engine.connect() as conn:
            # 使用 INFORMATION_SCHEMA 获取表信息，比 COUNT(*) 快很多
            result = conn.execute(text(f"""
                SELECT TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_SCHEMA = '{mysql_db}' AND TABLE_NAME = '{table_name}'
            """))
            row = result.fetchone()
            if row:
                return {
                    'estimated_rows': row[0] or 0,
                    'data_size_mb': round((row[1] or 0) / 1024 / 1024, 2),
                    'index_size_mb': round((row[2] or 0) / 1024 / 1024, 2)
                }
    except Exception as e:
        print(f"获取表 {table_name} 信息时出错: {e}")
    return {'estimated_rows': 0, 'data_size_mb': 0, 'index_size_mb': 0}


def get_foreign_keys_info():
    """获取当前数据库中的外键信息"""
    print("🔍 正在检查数据库外键信息...")
    inspector = inspect(engine)
    foreign_keys_info = {}

    # 检查每个表的外键
    tables = ['search_tasks', 'phone_results', 'email_results']
    for i, table_name in enumerate(tables):
        print_progress_bar(i, len(tables), '检查表', f'({table_name})')
        try:
            fks = inspector.get_foreign_keys(table_name)
            if fks:
                foreign_keys_info[table_name] = fks
                print(f"\n📋 表 {table_name} 的外键:")
                for fk in fks:
                    print(f"  - 约束名: {fk['name']}")
                    print(f"    本地列: {fk['constrained_columns']}")
                    print(f"    引用表: {fk['referred_table']}")
                    print(f"    引用列: {fk['referred_columns']}")
        except Exception as e:
            print(f"\n❌ 检查表 {table_name} 的外键时出错: {e}")

    print_progress_bar(len(tables), len(tables), '检查表', '完成')
    print()
    return foreign_keys_info


def remove_foreign_key_constraint(table_name, constraint_name):
    """删除指定表的外键约束 - 优化版本"""
    start_time = time.time()
    try:
        with engine.connect() as conn:
            # 使用事务确保操作的原子性
            trans = conn.begin()
            try:
                sql = f"ALTER TABLE {table_name} DROP FOREIGN KEY {constraint_name}"
                print(f"🔧 执行: DROP FOREIGN KEY {constraint_name} on {table_name}")

                conn.execute(text(sql))
                trans.commit()

                elapsed = time.time() - start_time
                print(f"✅ 成功删除外键约束 {constraint_name} (耗时: {elapsed:.2f}秒)")
                return True, elapsed
            except Exception as e:
                trans.rollback()
                elapsed = time.time() - start_time
                print(f"❌ 删除外键约束失败: {e} (耗时: {elapsed:.2f}秒)")
                return False, elapsed
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 连接数据库失败: {e} (耗时: {elapsed:.2f}秒)")
        return False, elapsed


def remove_foreign_key_parallel(fk_info):
    """并行删除外键约束的工作函数"""
    table_name, constraint_name = fk_info
    return remove_foreign_key_constraint(table_name, constraint_name)


def remove_all_foreign_keys(use_parallel=True):
    """删除所有外键约束 - 高性能版本"""
    print("=" * 80)
    print("🚀 大数据量外键删除工具 - 高性能版本")
    print("=" * 80)

    start_time = time.time()

    # 首先获取当前的外键信息
    print("\n📊 1. 获取数据库信息...")
    foreign_keys_info = get_foreign_keys_info()

    if not foreign_keys_info:
        print("✅ 数据库中没有发现外键约束")
        return True

    # 显示数据库规模信息
    print("\n📈 2. 数据库规模信息:")
    total_estimated_rows = 0
    for table_name in ['search_tasks', 'phone_results', 'email_results']:
        info = get_table_info_fast(table_name)
        total_estimated_rows += info['estimated_rows']
        print(f"   📋 {table_name}: ~{info['estimated_rows']:,} 行, "
              f"数据: {info['data_size_mb']}MB, 索引: {info['index_size_mb']}MB")

    print(f"\n📊 总计估算数据量: ~{total_estimated_rows:,} 行")

    # 准备删除外键约束
    fk_list = []
    for table_name, fks in foreign_keys_info.items():
        for fk in fks:
            fk_list.append((table_name, fk['name']))

    total_count = len(fk_list)
    print(f"\n🔧 3. 发现 {total_count} 个外键约束，开始删除...")

    success_count = 0
    total_time = 0

    if use_parallel and total_count > 1:
        print("⚡ 使用并行处理模式...")
        # 并行删除外键约束
        with ThreadPoolExecutor(max_workers=min(total_count, 3)) as executor:
            future_to_fk = {executor.submit(remove_foreign_key_parallel, fk_info): fk_info
                           for fk_info in fk_list}

            for i, future in enumerate(as_completed(future_to_fk)):
                fk_info = future_to_fk[future]
                try:
                    success, elapsed = future.result()
                    if success:
                        success_count += 1
                    total_time += elapsed
                    print_progress_bar(i + 1, total_count, '删除进度',
                                     f'({fk_info[1]} 完成)')
                except Exception as e:
                    print(f"\n❌ 处理 {fk_info} 时出错: {e}")
    else:
        print("🔄 使用串行处理模式...")
        # 串行删除外键约束
        for i, (table_name, constraint_name) in enumerate(fk_list):
            success, elapsed = remove_foreign_key_constraint(table_name, constraint_name)
            if success:
                success_count += 1
            total_time += elapsed
            print_progress_bar(i + 1, total_count, '删除进度',
                             f'({constraint_name} 完成)')

    print(f"\n\n📊 4. 删除完成统计:")
    print(f"   ⏱️  总耗时: {time.time() - start_time:.2f} 秒")
    print(f"   📈 总计外键约束: {total_count}")
    print(f"   ✅ 成功删除: {success_count}")
    print(f"   ❌ 删除失败: {total_count - success_count}")
    print(f"   ⚡ 平均处理时间: {total_time/total_count:.2f} 秒/个")

    if success_count == total_count:
        print("\n🎉 所有外键约束删除成功！")
        return True
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个外键约束删除失败")
        return False


def verify_data_integrity_fast(skip_verification=False):
    """快速验证数据完整性 - 大数据量优化版本"""
    if skip_verification:
        print("\n⏭️  跳过数据完整性验证（用户选择）")
        return True

    print("\n🔍 5. 快速验证数据完整性...")

    try:
        tables = ['search_tasks', 'phone_results', 'email_results']

        # 使用快速方法获取表信息而不是全表扫描
        print("   📊 使用快速估算方法（避免全表扫描）:")
        for table in tables:
            info = get_table_info_fast(table)
            print(f"   📋 {table}: ~{info['estimated_rows']:,} 行数据 "
                  f"(数据大小: {info['data_size_mb']}MB)")

        # 可选：对小表进行精确计数验证
        print("\n   🔍 对关键表进行采样验证:")
        with engine.connect() as conn:
            for table in ['search_tasks']:  # 只验证主表
                result = conn.execute(text(f"SELECT COUNT(*) as count FROM {table} LIMIT 1"))
                count = result.fetchone()[0]
                print(f"   ✅ {table}: {count:,} 行数据（精确计数）")

        print("\n✅ 数据完整性验证通过，所有数据都保持完整")
        print("   💡 提示：外键约束已删除，但所有数据列和内容都完整保留")
        return True
    except Exception as e:
        print(f"\n❌ 数据完整性验证失败: {e}")
        return False


def ask_user_confirmation():
    """询问用户确认"""
    print("\n" + "="*60)
    print("⚠️  重要提醒:")
    print("1. 此操作将删除所有外键约束")
    print("2. 数据和列将完全保留，不会丢失任何信息")
    print("3. 删除后可以自由删除数据而不受外键限制")
    print("4. 如需要可以稍后重新添加外键约束")
    print("="*60)

    while True:
        choice = input("\n是否继续？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            return True
        elif choice in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y 或 n")


def ask_verification_preference():
    """询问验证偏好"""
    print("\n📊 数据验证选项:")
    print("1. 快速验证（推荐，适合大数据量）")
    print("2. 跳过验证（最快，直接完成）")

    while True:
        choice = input("请选择 (1/2): ").strip()
        if choice == '1':
            return False  # 不跳过验证
        elif choice == '2':
            return True   # 跳过验证
        else:
            print("请输入 1 或 2")


def main():
    """主函数 - 大数据量优化版本"""
    print("🚀 数据库外键删除工具 - 大数据量优化版本")
    print("💡 专为百万级数据量设计的高性能处理")
    print("⚠️  注意：此工具只删除外键约束，绝不会删除任何数据！")
    print(f"🎯 目标数据库: {mysql_db}")

    try:
        # 测试数据库连接
        print("\n🔌 测试数据库连接...")
        start_time = time.time()
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        connection_time = time.time() - start_time
        print(f"✅ 数据库连接成功 (耗时: {connection_time:.2f}秒)")

        # 询问用户确认
        if not ask_user_confirmation():
            print("❌ 用户取消操作")
            return

        # 询问验证偏好
        skip_verification = ask_verification_preference()

        # 删除外键约束
        print(f"\n🚀 开始处理...")
        total_start_time = time.time()

        if remove_all_foreign_keys(use_parallel=True):
            # 验证数据完整性
            verify_data_integrity_fast(skip_verification)

            total_time = time.time() - total_start_time
            print("\n" + "🎉" * 20)
            print("🎉 外键删除操作完成！")
            print(f"⏱️  总耗时: {total_time:.2f} 秒")
            print("✅ 现在你可以自由删除数据而不用担心外键约束问题")
            print("💡 所有数据列和内容都完整保留")
            print("🔄 如需要可以稍后重新添加外键约束")
            print("🎉" * 20)
        else:
            print("\n⚠️  外键删除操作未完全成功，请检查错误信息")

    except KeyboardInterrupt:
        print("\n\n❌ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        print("💡 提示：如果是连接超时，可以尝试增加超时设置或检查网络连接")


if __name__ == "__main__":
    main()
