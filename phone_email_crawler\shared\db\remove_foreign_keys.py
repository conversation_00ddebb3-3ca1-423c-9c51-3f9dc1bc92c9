# -*- coding: utf-8 -*-
"""
删除数据库外键约束的脚本
注意：此脚本只删除外键约束，不删除数据和列
Author: AI Assistant
Create Time: 2025/9/8
File Name: remove_foreign_keys.py
"""

from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv

load_dotenv()

# 数据库配置
mysql_host = os.getenv('MYSQL_HOST', '************')
mysql_port = os.getenv('MYSQL_PORT', '13306')
mysql_user = os.getenv('MYSQL_USER', 'root')
mysql_password = os.getenv('MYSQL_PASSWORD', 'qwer1234')
mysql_db = os.getenv('MYSQL_DATABASE', 'phone_email_crawler')

# 创建数据库连接
DATABASE_URL = f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_db}"

engine = create_engine(
    DATABASE_URL,
    pool_recycle=1800,
    pool_pre_ping=True,
    pool_size=5,
    max_overflow=10,
    echo=True  # 显示SQL语句以便调试
)


def get_foreign_keys_info():
    """获取当前数据库中的外键信息"""
    inspector = inspect(engine)
    foreign_keys_info = {}
    
    # 检查每个表的外键
    for table_name in ['search_tasks', 'phone_results', 'email_results']:
        try:
            fks = inspector.get_foreign_keys(table_name)
            if fks:
                foreign_keys_info[table_name] = fks
                print(f"\n表 {table_name} 的外键:")
                for fk in fks:
                    print(f"  - 约束名: {fk['name']}")
                    print(f"    本地列: {fk['constrained_columns']}")
                    print(f"    引用表: {fk['referred_table']}")
                    print(f"    引用列: {fk['referred_columns']}")
        except Exception as e:
            print(f"检查表 {table_name} 的外键时出错: {e}")
    
    return foreign_keys_info


def remove_foreign_key_constraint(table_name, constraint_name):
    """删除指定表的外键约束"""
    try:
        with engine.connect() as conn:
            # 使用事务确保操作的原子性
            trans = conn.begin()
            try:
                sql = f"ALTER TABLE {table_name} DROP FOREIGN KEY {constraint_name}"
                print(f"执行SQL: {sql}")
                conn.execute(text(sql))
                trans.commit()
                print(f"✓ 成功删除表 {table_name} 的外键约束 {constraint_name}")
                return True
            except Exception as e:
                trans.rollback()
                print(f"✗ 删除外键约束失败: {e}")
                return False
    except Exception as e:
        print(f"✗ 连接数据库失败: {e}")
        return False


def remove_all_foreign_keys():
    """删除所有外键约束"""
    print("=" * 60)
    print("开始删除数据库外键约束")
    print("=" * 60)
    
    # 首先获取当前的外键信息
    print("\n1. 获取当前外键信息...")
    foreign_keys_info = get_foreign_keys_info()
    
    if not foreign_keys_info:
        print("✓ 数据库中没有发现外键约束")
        return True
    
    print(f"\n2. 发现 {sum(len(fks) for fks in foreign_keys_info.values())} 个外键约束，开始删除...")
    
    success_count = 0
    total_count = 0
    
    # 删除每个外键约束
    for table_name, fks in foreign_keys_info.items():
        for fk in fks:
            total_count += 1
            constraint_name = fk['name']
            if remove_foreign_key_constraint(table_name, constraint_name):
                success_count += 1
    
    print(f"\n3. 删除完成统计:")
    print(f"   总计外键约束: {total_count}")
    print(f"   成功删除: {success_count}")
    print(f"   删除失败: {total_count - success_count}")
    
    if success_count == total_count:
        print("\n✓ 所有外键约束删除成功！")
        return True
    else:
        print(f"\n✗ 有 {total_count - success_count} 个外键约束删除失败")
        return False


def verify_data_integrity():
    """验证数据完整性 - 确保数据没有被删除"""
    print("\n4. 验证数据完整性...")
    
    try:
        with engine.connect() as conn:
            # 检查每个表的数据行数
            tables = ['search_tasks', 'phone_results', 'email_results']
            for table in tables:
                result = conn.execute(text(f"SELECT COUNT(*) as count FROM {table}"))
                count = result.fetchone()[0]
                print(f"   表 {table}: {count} 行数据")
        
        print("✓ 数据完整性验证通过，所有数据都保持完整")
        return True
    except Exception as e:
        print(f"✗ 数据完整性验证失败: {e}")
        return False


def main():
    """主函数"""
    print("数据库外键删除工具")
    print("注意：此工具只删除外键约束，不会删除任何数据！")
    print(f"目标数据库: {mysql_db}")
    
    try:
        # 测试数据库连接
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("✓ 数据库连接成功")
        
        # 删除外键约束
        if remove_all_foreign_keys():
            # 验证数据完整性
            verify_data_integrity()
            print("\n" + "=" * 60)
            print("外键删除操作完成！")
            print("现在你可以自由删除数据而不用担心外键约束问题。")
            print("=" * 60)
        else:
            print("\n外键删除操作未完全成功，请检查错误信息。")
            
    except Exception as e:
        print(f"✗ 程序执行失败: {e}")


if __name__ == "__main__":
    main()
