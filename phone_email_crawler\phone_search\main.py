# -*- coding: utf-8 -*-
"""
Phone search main program
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/8/18
File Name: main.py
"""
import sys
import time
import logging
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from phone_email_crawler.phone_search.spider import PhoneSearchThreadManager
from phone_email_crawler.phone_search.config import PHONE_SEARCH_CONFIG

# 确保日志目录存在
if not os.path.exists('logs'):
    os.makedirs('logs')

# 配置日志
logging.basicConfig(
    level=getattr(logging, PHONE_SEARCH_CONFIG['log_level']),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(PHONE_SEARCH_CONFIG['log_file']),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('PhoneSearchMain')


def main():
    """电话搜索主程序"""
    # 直接使用配置文件中的参数
    threads = PHONE_SEARCH_CONFIG['threads']
    batch_size = PHONE_SEARCH_CONFIG['batch_size']
    max_batches = 0  # 0表示无限制

    logger.info("启动电话搜索爬虫")
    logger.info(f"配置参数:")
    logger.info(f"   - 线程数: {threads}")
    logger.info(f"   - 批处理大小: {batch_size}")
    logger.info(f"   - 爬取到最后一页 (无页数限制)")
    logger.info(f"   - 最大重试次数: {PHONE_SEARCH_CONFIG['max_retries']}")
    logger.info(f"   - 保存频率: {PHONE_SEARCH_CONFIG['save_frequency']}")
    
    try:
        total_processed = 0
        batch_count = 0
        successful_batches = 0
        empty_batches = 0
        max_empty_batches = 3  # 连续3次空批次后停止
        
        # 批处理循环
        while True:
            batch_count += 1
            
            # 检查是否达到最大批次数
            if max_batches > 0 and batch_count > max_batches:
                logger.info(f"达到最大批次数 {max_batches}，停止处理")
                break

            logger.info(f"开始第 {batch_count} 批处理")

            # 创建线程管理器
            manager = PhoneSearchThreadManager(
                num_threads=threads,
                batch_size=batch_size
            )

            # 开始批处理
            batch_start_time = time.time()
            started = manager.start_batch_processing(batch_size)
            
            if started:
                # 等待当前批次完成
                manager.wait_completion()
                
                batch_end_time = time.time()
                batch_duration = batch_end_time - batch_start_time
                
                # 统计批次结果
                batch_processed = manager.completed_queries
                total_processed += batch_processed
                
                if batch_processed > 0:
                    successful_batches += 1
                    empty_batches = 0  # 重置空批次计数
                    logger.info(f"✅ 第 {batch_count} 批完成: 处理了 {batch_processed} 个任务, 耗时 {batch_duration:.1f} 秒")
                else:
                    empty_batches += 1
                    logger.warning(f"⚠️ 第 {batch_count} 批: 无任务处理")
                
                # 批次间隔
                time.sleep(PHONE_SEARCH_CONFIG['batch_interval'])
            
            else:
                empty_batches += 1
                logger.info(f"📭 第 {batch_count} 批: 没有待处理任务")
                
                # 连续空批次检查
                if empty_batches >= max_empty_batches:
                    logger.info(f"🔚 连续 {max_empty_batches} 次没有任务，总共处理了 {total_processed} 个")
                    break
                
                # 等待后重试
                time.sleep(PHONE_SEARCH_CONFIG['batch_interval'] * 2)
        
        # 最终统计
        logger.info(f"🎉 ===== 电话搜索完成 =====")
        logger.info(f"📊 总计: {successful_batches} 个成功批次，处理了 {total_processed} 个任务")
        if total_processed > 0:
            avg_per_batch = total_processed / successful_batches if successful_batches > 0 else 0
            logger.info(f"📈 平均每批: {avg_per_batch:.1f} 个任务")
        logger.info(f"🎉 ========================")
    
    except KeyboardInterrupt:
        logger.info(f"⏹️ 用户停止: 已处理 {total_processed} 个任务")
    except Exception as e:
        logger.error(f"❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
