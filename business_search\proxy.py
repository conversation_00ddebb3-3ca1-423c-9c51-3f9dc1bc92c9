# -*- coding: utf-8 -*-
"""
Proxy management for business search
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/9/1
File Name: proxy.py
"""
import time
import random
import threading
import os
from typing import Optional, List
import requests
import logging


class GlobalProxyPool:
    """全局代理池管理器 - 单例模式（每个进程独立）"""
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        self._initialized = True

        self.proxy_url = "http://api.proxy.ip2world.com/getProxyIp?return_type=txt&protocol=http&num=500&regions=us&lb=1"
        self.proxy_list = []
        self.pool_lock = threading.Lock()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.min_proxies = 50  # 低于此数量时补充
        self.batch_size = 500  # 每次获取的代理数量
        self.minute = 10

        # 初始化时获取代理
        self.logger.info(f"业务搜索代理池初始化 - 实例ID: {id(self)} - 进程ID: {os.getpid()}")
        self._refill_proxies()

    def _refill_proxies(self):
        """从API获取新代理并补充到池中"""
        try:
            url = self.proxy_url
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                text = response.text
                # 检查响应内容有效性
                if '未加入白名单' in text or '认证失败' in text:
                    self.logger.error(f"代理API返回错误: {text[:100]}")
                    return

                # 解析代理列表
                new_proxies = [line.strip() for line in text.split('\n') if line.strip()]
                if new_proxies:
                    self.proxy_list.extend(new_proxies)
                    self.logger.info(f"成功补充代理池，新增{len(new_proxies)}个代理，当前总数: {len(self.proxy_list)}")
                else:
                    self.logger.warning("代理API返回空列表")
            else:
                self.logger.error(f"代理API请求失败，状态码: {response.status_code}")
        except Exception as e:
            self.logger.error(f"补充代理池失败: {e}")

    def get_proxy(self) -> Optional[str]:
        """从代理池获取一个代理（用完即删）"""
        with self.pool_lock:
            # 检查是否需要补充代理
            if len(self.proxy_list) < self.min_proxies:
                self.logger.info(f"代理池剩余{len(self.proxy_list)}个，开始补充...")
                self._refill_proxies()

            # 获取代理
            if self.proxy_list:
                proxy = self.proxy_list.pop(0)
                self.logger.debug(f"分配代理: {proxy}，剩余: {len(self.proxy_list)}个!!!!!")
                return proxy
            else:
                self.logger.warning("代理池为空，无法分配代理")
                return None

    def get_pool_size(self) -> int:
        """获取当前代理池大小"""
        with self.pool_lock:
            return len(self.proxy_list)


if __name__ == '__main__':
    # 测试代理管理器
    logging.basicConfig(level=logging.INFO)
    proxy_manager = GlobalProxyPool()
    proxy = proxy_manager.get_proxy()
    if proxy:
        print(f"获取到代理: {proxy}")
    else:
        print("未获取到代理")
