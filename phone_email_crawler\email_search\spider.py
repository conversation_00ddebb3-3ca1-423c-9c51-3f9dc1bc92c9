# -*- coding: utf-8 -*-
"""
Email search spider for phone and email crawler
Author: ji<PERSON> wei
Create Time: 2025/8/18
File Name: spider.py
"""
import random
import time
import re
import logging
import threading
from datetime import datetime
from typing import List, Tuple, Optional, Dict

import requests
from lxml import etree
from sqlalchemy import func

import sys
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from phone_email_crawler.shared.cookies import GoogleCookie
from phone_email_crawler.shared.proxy import GlobalProxyPool
from phone_email_crawler.shared.db.init_db import get_db_session
from phone_email_crawler.shared.db.db_models import SearchTasks, PhoneResults, EmailResults
from phone_email_crawler.email_search.config import EMAIL_SEARCH_CONFIG, USER_AGENTS, BASE_HEADERS

# 配置日志
logging.basicConfig(
    level=getattr(logging, EMAIL_SEARCH_CONFIG['log_level']),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(EMAIL_SEARCH_CONFIG['log_file']),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('EmailSearch')


class EmailSearchSpider:
    """邮箱搜索爬虫"""
    
    def __init__(self, thread_id=0):
        self.thread_id = thread_id

        # Cookie管理
        cookie_file = f"cookies/{EMAIL_SEARCH_CONFIG['cookie_prefix']}{thread_id}.json"
        self.google_cookie = GoogleCookie(cookie_file=cookie_file)

        # 全局代理池
        self.global_proxy_pool = GlobalProxyPool()

        # 当前代理管理
        self.current_proxy = None
        self.proxy_usage_count = 0
        self.proxy_error_count = 0
        self.max_proxy_usage = EMAIL_SEARCH_CONFIG['max_proxy_usage']
        self.max_errors_before_proxy_change = EMAIL_SEARCH_CONFIG['max_errors_before_proxy_change']

        # 重试机制参数
        self.cookie_failure_count = 0
        self.max_cookie_failures = EMAIL_SEARCH_CONFIG['max_cookie_failures']

        # 线程锁
        self.lock = threading.Lock()

        logger.info(f"[线程 {self.thread_id}] 邮箱搜索爬虫初始化完成")
    
    def get_headers(self) -> Dict[str, str]:
        """生成带有随机User-Agent的请求头"""
        headers = BASE_HEADERS.copy()
        headers['user-agent'] = random.choice(USER_AGENTS)
        return headers
    
    def extract_emails(self, content: str) -> List[str]:
        """从内容中提取所有邮箱地址"""
        if not content:
            return []

        found_emails = []

        # 基本邮箱正则表达式 - 更严格的域名匹配
        basic_pattern = r'[\w\.-]+@[\w\.-]+\.(?:com|net|org|edu|gov|mil|io|co|uk|cn|de|fr|jp|au|ru|ca|it|nl|es|br|in|mx|ch|se|no|dk|fi|pl|cz|at|be|ie|nz|sg|hu|pt|gr|il|za|tr|ro|hk|kr|tw|vn|id|th|my|ph|sa|ae|qa|pk|bd|ng|ke|tz|gh|et|dz|ma|tn|eg|ly|zw|zm|mw|mu|mg|ci|cm|sn|cd|ao|na|bw|ls|sz|rw|bi|tg|bj|ne|ml|mr|td|gm|sl|lr|gn|gw|cv|st|ga|cg|cf|sd|ss|er|dj|so)'

        # 提取所有标准邮箱
        emails = re.findall(basic_pattern, content, re.IGNORECASE)
        for email in emails:
            email = email.strip()
            if email and email not in found_emails:
                found_emails.append(email)

        # 处理带空格的邮箱
        spaced_email_pattern = r'([\w\.-]+)\s*@\s*([\w\.-]+)\s*\.\s*(com|net|org|edu|gov|mil|io|co|uk|cn|de|fr|jp|au|ru|ca|it|nl|es|br|in|mx)'
        spaced_matches = re.findall(spaced_email_pattern, content, re.IGNORECASE)
        for match in spaced_matches:
            email = f"{match[0]}@{match[1]}.{match[2]}"
            if email not in found_emails:
                found_emails.append(email)

        # 针对特殊情况：邮箱后面紧跟着句点和文本
        special_pattern = r'([\w\.-]+)\s*@\s*([\w\.-]+)\s*\.\s*(com|net|org|edu|gov|mil|io|co|uk)(?:\s*\.\s*|\s+)'
        special_matches = re.findall(special_pattern, content, re.IGNORECASE)
        for match in special_matches:
            email = f"{match[0]}@{match[1]}.{match[2]}"
            if email not in found_emails:
                found_emails.append(email)

        # 尝试查找并组合分散的邮箱部分
        if '@' in content and not found_emails:
            combined_pattern = r'([\w\.-]+)\s*@\s*([\w\.-]+)\s*\.\s*([a-zA-Z]{2,6})'
            combined_matches = re.findall(combined_pattern, content)
            for match in combined_matches:
                username = match[0].strip()
                domain = match[1].strip()
                tld = match[2].strip()
                email = f"{username}@{domain}.{tld}"
                if email not in found_emails:
                    found_emails.append(email)

        return found_emails
    
    def check_cookies_valid(self, page) -> bool:
        """检查页面是否有有效的搜索结果（Cookie是否有效）"""
        try:
            h3_elements = page.xpath('//h3')
            return len(h3_elements) >= 1
        except Exception:
            return False
    
    def increment_cookie_failure(self) -> bool:
        """增加Cookie失败计数，如果达到阈值则重置Cookie"""
        self.cookie_failure_count += 1
        logger.warning(f"[线程 {self.thread_id}] Cookie失败计数: {self.cookie_failure_count}/{self.max_cookie_failures}")

        if self.cookie_failure_count >= self.max_cookie_failures:
            self.reset_cookie()
            return True
        return False
    
    def reset_cookie(self):
        """重置Cookie并重置失败计数器"""
        max_retries = EMAIL_SEARCH_CONFIG['cookie_reset_retries']
        retry_delay = EMAIL_SEARCH_CONFIG['cookie_reset_delay']
        
        try:
            with self.lock:
                logger.info(f"[线程 {self.thread_id}] Cookie累计失败{self.cookie_failure_count}次，重新生成Cookie")
                
                for attempt in range(max_retries):
                    try:
                        logger.info(f"[线程 {self.thread_id}] 尝试获取新Cookie (第{attempt + 1}/{max_retries}次)")
                        new_cookies = self.google_cookie.create_cookies()
                        
                        if new_cookies and self.google_cookie.is_cookies_valid(new_cookies):
                            self.google_cookie.cookies = new_cookies
                            self.cookie_failure_count = 0
                            logger.info(f"[线程 {self.thread_id}] 新Cookie已生成")
                            return
                        else:
                            logger.warning(f"[线程 {self.thread_id}] 第{attempt + 1}次获取的Cookie无效")
                            if attempt < max_retries - 1:
                                time.sleep(retry_delay)
                    
                    except Exception as retry_e:
                        logger.error(f"[线程 {self.thread_id}] 第{attempt + 1}次获取Cookie时出错: {retry_e}")
                        if attempt < max_retries - 1:
                            time.sleep(retry_delay)
                
                logger.error(f"[线程 {self.thread_id}] 经过{max_retries}次尝试，仍无法获取有效Cookie")
        
        except Exception as e:
            logger.error(f"[线程 {self.thread_id}] 重置Cookie失败: {e}")

    def get_current_proxy(self) -> Optional[str]:
        """获取当前可用的代理"""
        # 检查是否需要更换代理
        need_new_proxy = (
            self.current_proxy is None or  # 没有代理
            self.proxy_usage_count >= self.max_proxy_usage or  # 使用次数达到上限
            self.proxy_error_count >= self.max_errors_before_proxy_change  # 错误次数达到上限
        )

        if need_new_proxy:
            # 获取新代理
            new_proxy = self.global_proxy_pool.get_proxy()
            if new_proxy:
                self.current_proxy = new_proxy
                self.proxy_usage_count = 0
                self.proxy_error_count = 0
                logger.info(f"[线程 {self.thread_id}] 更换新代理: {new_proxy}")
            else:
                logger.warning(f"[线程 {self.thread_id}] 无法获取新代理")
                return None

        return self.current_proxy

    def record_proxy_usage(self, success: bool):
        """记录代理使用情况"""
        if self.current_proxy:
            self.proxy_usage_count += 1
            if not success:
                self.proxy_error_count += 1
                logger.debug(f"[线程 {self.thread_id}] 代理错误计数: {self.proxy_error_count}/{self.max_errors_before_proxy_change}")
            else:
                # 成功时重置错误计数
                self.proxy_error_count = 0

    def search_email_page(self, search_query: str, page_num: int = 0,
                         max_retries: int = 2) -> Dict:
        """搜索特定页码的邮箱地址结果"""
        
        # 搜索参数
        params = {
            'q': search_query,
            'oq': search_query,
            'start': page_num * 10,
            'gs_lcrp': '',
            'sourceid': 'chrome',
            'ie': 'UTF-8',
        }

        # 获取当前代理
        proxy = self.get_current_proxy()
        proxy_server = None
        if proxy:
            proxy_server = {
                "http": f"http://{proxy}",
                "https": f"http://{proxy}"
            }
        
        for attempt in range(max_retries):
            try:
                cookies = self.google_cookie.cookies

                # 确保Cookie不为空
                if not cookies:
                    logger.warning(f"[线程 {self.thread_id}] Cookie为空，尝试重新生成")
                    self.reset_cookie()
                    cookies = self.google_cookie.cookies
                    if not cookies:
                        logger.error(f"[线程 {self.thread_id}] 重新生成Cookie失败，跳过此次请求")
                        self.record_proxy_usage(False)
                        continue

                headers = self.get_headers()

                response = requests.get(
                    url='https://www.google.com/search',
                    headers=headers,
                    cookies=cookies,
                    params=params,
                    timeout=EMAIL_SEARCH_CONFIG['request_timeout'],
                    proxies=proxy_server
                )
                
                if response.status_code == 200:
                    page = etree.HTML(response.text)

                    # 检查Cookie是否有效
                    if not self.check_cookies_valid(page):
                        logger.info(f"[线程 {self.thread_id}] 页面没有有效结果，Cookie可能失效")
                        self.increment_cookie_failure()
                        self.record_proxy_usage(False)  # 记录代理使用失败
                        continue
                    
                    # 提取搜索结果
                    url_elements = page.xpath('//a[@jsname="UWckNb"]/@href')
                    
                    # 获取URL和内容
                    urls = [url for url in url_elements]
                    contents = []
                    for i in range(len(url_elements)):
                        content_xpath = f'(//div[@jscontroller="SC7lYd"]//*[@data-snf="nke7rc"])[{i + 1}]//text()'
                        content_texts = page.xpath(content_xpath)
                        content = ' '.join(content_texts)
                        contents.append(content)
                    
                    # 提取邮箱地址结果
                    results = []
                    for i, url in enumerate(urls):
                        try:
                            content = contents[i] if i < len(contents) else ''
                            emails = self.extract_emails(content)
                            for email in emails:
                                results.append((url, email))
                        except Exception as e:
                            logger.error(f"[线程 {self.thread_id}] 提取结果 {i + 1} 时出错: {e}")
                    
                    # 检查是否有下一页
                    has_next = page.xpath('//a[@id="pnnext"]')

                    # 记录代理使用成功
                    self.record_proxy_usage(True)

                    return {
                        'results': results,
                        'has_next': len(has_next) > 0,
                        'success': True
                    }
                
                elif response.status_code == 429:
                    logger.warning(f"[线程 {self.thread_id}] 请求被限制 (429)，Cookie可能失效")
                    self.increment_cookie_failure()
                    self.record_proxy_usage(False)

                else:
                    logger.warning(f"[线程 {self.thread_id}] 请求返回状态码: {response.status_code}")
                    self.increment_cookie_failure()
                    self.record_proxy_usage(False)

            except Exception as e:
                logger.error(f'[线程 {self.thread_id}] 请求错误: {e}, 尝试第 {attempt + 1}/{max_retries} 次')
                self.increment_cookie_failure()
                self.record_proxy_usage(False)

        logger.error(f"[线程 {self.thread_id}] 所有 {max_retries} 次尝试均失败")
        return {
            'results': [],
            'has_next': False,
            'success': False
        }

    def search_emails(self, phone_number: str) -> Tuple[List[Tuple[int, str, str]], int]:
        """搜索邮箱地址，爬取到最后一页，返回(结果列表, 总页数)"""
        search_query = f'"{phone_number}" email'
        logger.info(f"[线程 {self.thread_id}] 开始搜索: {search_query}")

        all_results = []
        page_num = 0
        has_next = True

        while has_next:
            # 搜索当前页
            page_result = self.search_email_page(
                search_query, page_num,
                EMAIL_SEARCH_CONFIG['max_retries']
            )

            # 检查是否有结果
            if not page_result['results'] and not page_result['has_next']:
                logger.error(f"[线程 {self.thread_id}] 获取第 {page_num + 1} 页失败，停止搜索")
                break

            # 添加当前页面的结果
            for url, email in page_result['results']:
                all_results.append((page_num + 1, url, email))

            has_next = page_result['has_next']
            page_num += 1

            # 页面间延迟
            if has_next:
                delay = random.uniform(
                    EMAIL_SEARCH_CONFIG['request_delay_min'],
                    EMAIL_SEARCH_CONFIG['request_delay_max']
                )
                time.sleep(delay)

        logger.info(f"[线程 {self.thread_id}] 完成搜索 '{search_query}'，共爬取 {page_num} 页，找到 {len(all_results)} 个邮箱")
        return all_results, page_num


class EmailSearchThreadManager:
    """邮箱搜索线程管理器"""

    def __init__(self, num_threads: int = None, batch_size: int = None):
        self.num_threads = num_threads or EMAIL_SEARCH_CONFIG['threads']
        self.batch_size = batch_size or EMAIL_SEARCH_CONFIG['batch_size']
        self.save_frequency = EMAIL_SEARCH_CONFIG['save_frequency']

        self.result_lock = threading.Lock()
        self.threads = []
        self.completed_queries = 0

        logger.info(f"邮箱搜索管理器初始化: {self.num_threads} 个线程, 批处理大小 {self.batch_size}")

    def get_pending_email_tasks(self, batch_size: int = 15) -> List[PhoneResults]:
        """获取待处理的邮箱搜索任务（基于电话号码级别的状态）"""
        db = get_db_session()
        try:
            # 直接查询email_search_status为pending的电话结果
            phone_results = db.query(PhoneResults).filter(
                PhoneResults.email_search_status == 'pending',
                PhoneResults.phone_number.isnot(None)
            ).order_by(func.random()).limit(batch_size).all()

            logger.info(f"获取到 {len(phone_results)} 个待处理的邮箱搜索任务")
            return phone_results
        except Exception as e:
            logger.error(f"获取邮箱搜索任务失败: {e}")
            return []
        finally:
            db.close()

    def save_email_results(self, phone_result: PhoneResults,
                          search_query: str, results: List[Tuple[int, str, str]],
                          total_pages: int):
        """保存邮箱搜索结果到数据库"""
        db = get_db_session()
        try:
            email_results = []
            for page_num, url, email in results:
                email_result = EmailResults(
                    task_id=phone_result.task_id,
                    phone_result_id=phone_result.id,
                    keyword=phone_result.keyword,
                    region=phone_result.region,
                    phone_number=phone_result.phone_number,
                    search_query=search_query,
                    url=url,
                    email_address=email,
                    current_page=page_num
                )
                email_results.append(email_result)

            if email_results:
                db.add_all(email_results)
                logger.info(f"保存了 {len(email_results)} 个邮箱搜索结果")

            # 更新电话结果的邮箱搜索状态：只有当页数和结果数同时为0时才不更新状态
            if total_pages == 0 and len(results) == 0:
                logger.info(f"[电话 {phone_result.phone_number}] 页数和邮箱数量都是0，不更新email_search_status（可能是爬虫失败）")
                # 跳过状态更新，保持原有状态
            else:
                # 只有当有页数或有结果时才标记为completed
                phone_result.email_search_status = 'completed'
                logger.info(f"电话 {phone_result.phone_number} 邮箱搜索状态已更新为completed")

                # 检查该任务是否所有电话的邮箱搜索都完成了
                self.check_and_update_task_email_status(db, phone_result.task_id)

            db.commit()

        except Exception as e:
            logger.error(f"保存邮箱搜索结果失败: {e}")
            db.rollback()
        finally:
            db.close()

    def batch_save_email_results(self, batch_results: List[Dict]):
        """批量保存邮箱搜索结果到数据库"""
        if not batch_results:
            return

        db = get_db_session()
        try:
            all_email_results = []
            phone_result_updates = []

            for batch_item in batch_results:
                phone_result = batch_item['phone_result']
                search_query = batch_item['search_query']
                results = batch_item['results']
                total_pages = batch_item['total_pages']

                # 收集邮箱结果
                for page_num, url, email in results:
                    email_result = EmailResults(
                        task_id=phone_result.task_id,
                        phone_result_id=phone_result.id,
                        keyword=phone_result.keyword,
                        region=phone_result.region,
                        phone_number=phone_result.phone_number,
                        search_query=search_query,
                        url=url,
                        email_address=email,
                        current_page=page_num
                    )
                    all_email_results.append(email_result)

                # 收集电话结果状态更新
                if not (total_pages == 0 and len(results) == 0):
                    phone_result_updates.append(phone_result.id)

            # 批量插入邮箱结果
            if all_email_results:
                db.add_all(all_email_results)
                logger.info(f"批量保存了 {len(all_email_results)} 个邮箱搜索结果")

            # 批量更新电话结果的邮箱搜索状态
            if phone_result_updates:
                db.query(PhoneResults).filter(PhoneResults.id.in_(phone_result_updates)).update(
                    {PhoneResults.email_search_status: 'completed'},
                    synchronize_session=False
                )
                logger.info(f"批量更新了 {len(phone_result_updates)} 个电话结果的邮箱搜索状态")

            db.commit()

        except Exception as e:
            logger.error(f"批量保存邮箱搜索结果失败: {e}")
            db.rollback()
        finally:
            db.close()



    def email_worker(self, thread_id: int, phone_results: List[PhoneResults]):
        """邮箱搜索工作线程"""
        if not phone_results:
            logger.info(f"[线程 {thread_id}] 没有分配到任务")
            return

        spider = EmailSearchSpider(thread_id=thread_id)
        completed = 0

        # 批量保存相关变量
        batch_results = []
        batch_count = 0

        for phone_result in phone_results:
            try:
                logger.info(f"[线程 {thread_id}] 开始处理电话 {phone_result.phone_number} (任务 {phone_result.task_id})")

                # 执行邮箱搜索
                results, total_pages = spider.search_emails(phone_result.phone_number)

                # 收集结果到批次中
                search_query = f'"{phone_result.phone_number}" email'
                batch_results.append({
                    'phone_result': phone_result,
                    'search_query': search_query,
                    'results': results,
                    'total_pages': total_pages
                })

                batch_count += 1
                completed += 1

                if results:
                    logger.info(f"[线程 {thread_id}] 电话 {phone_result.phone_number} 完成，爬取 {total_pages} 页，找到 {len(results)} 个邮箱")
                else:
                    logger.warning(f"[线程 {thread_id}] 电话 {phone_result.phone_number} 爬取 {total_pages} 页，未找到邮箱")

                # 达到保存频率时批量保存
                if batch_count >= self.save_frequency:
                    self.batch_save_email_results(batch_results)
                    batch_results = []
                    batch_count = 0
                    logger.info(f"[线程 {thread_id}] 批量保存了 {self.save_frequency} 个任务的结果,")

                # 添加任务间延迟
                delay = random.uniform(1.0, 3.0)
                time.sleep(delay)

            except Exception as e:
                logger.error(f"[线程 {thread_id}] 处理电话 {phone_result.phone_number} 时出错: {e}")
                continue

        # 保存剩余的结果
        if batch_results:
            self.batch_save_email_results(batch_results)
            logger.info(f"[线程 {thread_id}] 最终批量保存了 {len(batch_results)} 个任务的结果")

        with self.result_lock:
            self.completed_queries += completed

        logger.info(f"[线程 {thread_id}] 完成 {completed} 个任务")

    def start_batch_processing(self, batch_size: int = None) -> bool:
        """启动批处理模式"""
        batch_size = batch_size or self.batch_size

        # 获取待处理任务
        phone_results = self.get_pending_email_tasks(batch_size)
        if not phone_results:
            logger.info("没有待处理的邮箱搜索任务")
            return False

        # 分配任务给线程
        tasks_per_thread = len(phone_results) // self.num_threads
        remainder = len(phone_results) % self.num_threads

        self.threads = []
        start_idx = 0

        for i in range(self.num_threads):
            # 计算当前线程的任务数量
            current_tasks_count = tasks_per_thread + (1 if i < remainder else 0)
            if current_tasks_count == 0:
                break

            # 分配任务
            thread_tasks = phone_results[start_idx:start_idx + current_tasks_count]
            start_idx += current_tasks_count

            # 创建并启动线程
            thread = threading.Thread(
                target=self.email_worker,
                args=(i, thread_tasks)
            )
            thread.daemon = True
            self.threads.append(thread)
            thread.start()

            logger.info(f"线程 {i} 已启动，处理 {len(thread_tasks)} 个任务")

        logger.info(f"已启动 {len(self.threads)} 个邮箱搜索线程")
        return True

    def wait_completion(self):
        """等待所有线程完成"""
        for thread in self.threads:
            thread.join()
        logger.info("所有邮箱搜索线程已完成")

