#!/bin/bash

# All Crawlers Starter
# Author: ji<PERSON> <PERSON>i
# Create Time: 2025/8/18
# File Name: start_all_crawlers.sh
# Description: Starts both phone and email crawlers

SCRIPT_DIR="/root/google-search/phone_email_crawler/sh"

echo "🚀 启动所有爬虫..."

# Start phone crawler
echo "📞 启动电话爬虫..."
nohup bash "$SCRIPT_DIR/start_phone_crawler.sh" > /dev/null 2>&1 &
PHONE_PID=$!
echo "✅ 电话爬虫已启动 (PID: $PHONE_PID)"

# Wait a moment
sleep 2

# Start email crawler
echo "📧 启动邮箱爬虫..."
nohup bash "$SCRIPT_DIR/start_email_crawler.sh" > /dev/null 2>&1 &
EMAIL_PID=$!
echo "✅ 邮箱爬虫已启动 (PID: $EMAIL_PID)"

echo ""
echo "🎉 所有爬虫已启动完成！"
echo "📞 电话爬虫PID: $PHONE_PID"
echo "📧 邮箱爬虫PID: $EMAIL_PID"
echo ""
echo "📋 管理命令:"
echo "  查看电话爬虫日志: tail -f phone_search/logs/phone_cycle.log"
echo "  查看邮箱爬虫日志: tail -f email_search/logs/email_cycle.log"
echo "  停止电话爬虫: bash sh/stop_phone_crawler.sh"
echo "  停止邮箱爬虫: bash sh/stop_email_crawler.sh"
echo "  停止所有爬虫: bash sh/stop_all_crawlers.sh"
