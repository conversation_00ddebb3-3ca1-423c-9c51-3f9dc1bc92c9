#!/bin/bash

# Phone Crawler Stopper
# Author: ji<PERSON> <PERSON>i
# Create Time: 2025/8/18
# File Name: stop_phone_crawler.sh
# Description: Stops phone search crawler processes

PROJECT_DIR="/root/google-search/phone_email_crawler"
PID_FILE="$PROJECT_DIR/phone_search/logs/phone_crawler.pid"
LOG_FILE="$PROJECT_DIR/phone_search/logs/phone_cycle.log"

# Function to log messages with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

echo "📞 停止电话爬虫进程..."

# Stop using PID file first
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        echo "⏹️ 停止电话爬虫循环脚本 (PID: $PID)"
        kill -TERM "$PID" 2>/dev/null
        sleep 3
        if kill -0 "$PID" 2>/dev/null; then
            echo "🔨 强制停止电话爬虫循环脚本"
            kill -KILL "$PID" 2>/dev/null
        fi
        log_message "🛑 电话爬虫循环脚本已停止"
    fi
    rm -f "$PID_FILE"
else
    echo "⚠️ 未找到PID文件，尝试通过进程名停止..."
fi

# Kill phone crawler main.py processes
echo "🔍 查找并停止电话爬虫主程序..."
PHONE_PIDS=$(ps aux | grep "phone_search/main.py" | grep -v grep | awk '{print $2}')
if [ ! -z "$PHONE_PIDS" ]; then
    echo "📞 停止电话爬虫主程序进程: $PHONE_PIDS"
    echo "$PHONE_PIDS" | xargs -r kill -TERM 2>/dev/null
    sleep 3
    # Force kill if still running
    REMAINING_PIDS=$(ps aux | grep "phone_search/main.py" | grep -v grep | awk '{print $2}')
    if [ ! -z "$REMAINING_PIDS" ]; then
        echo "🔨 强制停止残留的电话爬虫进程: $REMAINING_PIDS"
        echo "$REMAINING_PIDS" | xargs -r kill -KILL 2>/dev/null
    fi
    log_message "🛑 电话爬虫主程序已停止"
else
    echo "✅ 未发现运行中的电话爬虫主程序"
fi

# Kill start_phone_crawler.sh processes
echo "🔍 查找并停止电话爬虫启动脚本..."
START_PIDS=$(ps aux | grep "start_phone_crawler.sh" | grep -v grep | grep -v $$ | awk '{print $2}')
if [ ! -z "$START_PIDS" ]; then
    echo "📜 停止电话爬虫启动脚本进程: $START_PIDS"
    echo "$START_PIDS" | xargs -r kill -TERM 2>/dev/null
    sleep 2
    # Force kill if still running
    REMAINING_START_PIDS=$(ps aux | grep "start_phone_crawler.sh" | grep -v grep | grep -v $$ | awk '{print $2}')
    if [ ! -z "$REMAINING_START_PIDS" ]; then
        echo "🔨 强制停止残留的启动脚本进程: $REMAINING_START_PIDS"
        echo "$REMAINING_START_PIDS" | xargs -r kill -KILL 2>/dev/null
    fi
    log_message "🛑 电话爬虫启动脚本已停止"
else
    echo "✅ 未发现运行中的电话爬虫启动脚本"
fi

# Optional: Clean up browser processes
CLEANUP_SCRIPT="$PROJECT_DIR/sh/cleanup_browsers.sh"
if [ -f "$CLEANUP_SCRIPT" ]; then
    echo "🧹 清理浏览器进程..."
    bash "$CLEANUP_SCRIPT"
fi

echo "✅ 电话爬虫停止完成！"
log_message "✅ 电话爬虫完全停止"
