#!/bin/bash

# Clean browser processes and memory for business search
# Author: ji<PERSON> <PERSON>i
# Create Time: 2025/9/3
# File Name: cleanup_browsers.sh

echo "🧹 开始清理浏览器进程..."

# Find browser processes
pids=$(ps -ef | grep -E 'chromium|chrome|headless-shell|playwright' | grep -v grep | awk '{print $2}')

if [ -n "$pids" ]; then
    echo "🔍 发现 $(echo $pids | wc -w) 个浏览器进程，正在清理..."
    kill -15 $pids 2>/dev/null
    sleep 2

    # Force stop remaining processes
    remaining=$(ps -ef | grep -E 'chromium|chrome|headless-shell|playwright' | grep -v grep | awk '{print $2}')
    if [ -n "$remaining" ]; then
        echo "🔨 强制停止残留进程..."
        kill -9 $remaining 2>/dev/null
    fi
    echo "✅ 浏览器进程清理完成"
else
    echo "✅ 未发现需要清理的浏览器进程"
fi

# Clean temp files silently
echo "🗑️ 清理临时文件..."
find /tmp -name 'playwright*' -exec rm -rf {} \; 2>/dev/null
find /tmp -name 'chrome*' -exec rm -rf {} \; 2>/dev/null

echo "✅ 浏览器清理完成"
