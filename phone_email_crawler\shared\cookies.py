# -*- coding: utf-8 -*-
"""
Cookie management for phone and email crawler
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/8/18
File Name: cookies.py
"""
import asyncio
import random
import logging
import json
import os
from playwright.async_api import async_playwright
from phone_email_crawler.shared.proxy import GlobalProxyPool
try:
    from faker import Faker
    fake = Faker('en_US')  # 使用英文
    FAKER_AVAILABLE = True
except ImportError:
    FAKER_AVAILABLE = False
    fake = None

# 确保cookies目录存在
if not os.path.exists('cookies'):
    os.makedirs('cookies')


class HumanBehaviorSimulator:
    """人类行为模拟工具类"""

    @staticmethod
    def get_random_viewport():
        """生成随机设备视口"""
        return {
            "width": random.choice([1920, 1366, 1536, 1440]),
            "height": random.choice([1080, 768, 864, 900])
        }

    @staticmethod
    async def simulate_typing(element, text):
        """模拟人类输入行为"""
        for char in text:
            await element.type(char)
            await asyncio.sleep(random.uniform(0.08, 0.15))
            if random.random() < 0.07:
                await element.press("Backspace")
                await element.type(char)

    @staticmethod
    async def random_mouse_movement(page):
        """生成随机鼠标轨迹"""
        for _ in range(random.randint(3, 5)):
            x = random.randint(0, page.viewport_size['width'])
            y = random.randint(0, page.viewport_size['height'])
            await page.mouse.move(x, y, steps=random.randint(2, 5))
            await asyncio.sleep(random.uniform(0.3, 0.7))

    @staticmethod
    async def simulate_scroll(page):
        """模拟自然滚动"""
        scroll_distance = random.randint(300, 800)
        await page.mouse.wheel(0, scroll_distance)
        await asyncio.sleep(random.uniform(0.5, 1.2))

    @staticmethod
    async def random_page_interaction(page):
        """随机页面交互"""
        actions = [
            lambda: page.mouse.wheel(0, random.randint(200, 500)),
            lambda: HumanBehaviorSimulator.random_mouse_movement(page),
            lambda: page.keyboard.press('PageDown'),
            lambda: asyncio.sleep(random.uniform(0.5, 1.5))
        ]
        for _ in range(random.randint(2, 4)):
            await random.choice(actions)()
            await asyncio.sleep(random.uniform(0.3, 0.8))


class GoogleCookie:
    """谷歌Cookie类，合并了管理和获取功能"""

    def __init__(self, cookie_file="cookies/google_cookies.json"):
        self.cookies = {}
        self.cookie_file = cookie_file
        self.human_simulator = HumanBehaviorSimulator()
        self.config = {
            'timeout': 50000,
            'headless': True  # 用户已修改为False
        }
        self.load_cookies_from_file()
        
    def load_cookies_from_file(self):
        """从文件加载cookies"""
        if os.path.exists(self.cookie_file):
            try:
                with open(self.cookie_file, 'r') as f:
                    self.cookies = json.load(f)
                if self.cookies == {}:
                    logging.info("Cookie文件为空，尝试获取新的cookies")
                    new_cookies = self.create_cookies()
                    if new_cookies and self.is_cookies_valid(new_cookies):
                        self.cookies = new_cookies
                        logging.info("成功获取新的cookies")
                    else:
                        logging.warning("获取新cookies失败，保持空状态")
                        self.cookies = {}
                else:
                    logging.info("Cookies loaded from file")
            except:
                logging.warning("Failed to load cookies from file")
                self.cookies = {}
    
    def save_cookies_to_file(self):
        """保存cookies到文件"""
        with open(self.cookie_file, 'w') as f:
            json.dump(self.cookies, f)
        logging.info("Cookies saved to file")

    def get_cookies(self):
        """获取cookies"""
        return self.cookies

    def update_cookies(self, new_cookies):
        """更新cookies"""
        self.cookies = new_cookies
        self.save_cookies_to_file()
    
    def is_cookies_valid(self, cookies=None):
        """检查cookies是否有效

        Args:
            cookies: 要检查的cookies字典，如果为None则检查self.cookies
        """
        # 如果没有传入cookies参数，则检查self.cookies
        cookies_to_check = cookies if cookies is not None else self.cookies

        if not cookies_to_check:
            return False

        # 简单检查cookie是否包含必要的字段
        required_cookies = ['AEC', 'NID']
        for cookie in required_cookies:
            if cookie not in cookies_to_check:
                return False

        return True
    
    async def _detect_captcha(self, page):
        """检测是否出现验证码"""
        captcha_selectors = [
            'form#captcha-form',
            'div.g-recaptcha',
            'input[name="captcha"]',
            'title:has-text("unusual traffic")'
        ]

        for selector in captcha_selectors:
            if await page.query_selector(selector):
                logging.warning("检测到验证码!")
                return True
        return False

    async def _perform_search(self, page, query):
        """执行搜索操作"""
        logging.info(f"正在执行搜索操作: {query}")
        await page.goto('https://www.google.com', timeout=self.config.get('timeout', 30000))
        await page.wait_for_load_state('networkidle')

        search_box = await page.wait_for_selector('textarea[name="q"]', timeout=15000)
        await HumanBehaviorSimulator.simulate_typing(search_box, query)
        await asyncio.sleep(random.uniform(0.8, 1.3))
        await search_box.press('Enter')
        await page.wait_for_selector('div#search', timeout=self.config.get('timeout', 30000))
        return not await self._detect_captcha(page)

    async def fetch_new_cookies(self, search_query="python programming"):
        """获取谷歌搜索页面的cookies"""
        async with async_playwright() as p:
            # 获取代理
            # 从全局代理池获取代理

            global_proxy_pool = GlobalProxyPool()
            proxy = global_proxy_pool.get_proxy()
            if proxy and ':' in proxy:
                proxy_server = {'server': f'http://{proxy}'}
            else:
                proxy_server = None
            # 随机选择一个浏览器UA
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
            ]
            # 浏览器启动选项
            browser_args = [
                '--disable-blink-features=AutomationControlled',
                '--disable-features=IsolateOrigins,site-per-process',
                '--no-sandbox'
            ]

            # 浏览器配置
            browser_options = {
                'headless': self.config.get('headless', True),
                'args': browser_args,
                "ignore_default_args": ['--enable-automation']
            }
            logging.info(f"使用代理服务器: {proxy_server}  获取cookies")
            browser = await p.chromium.launch(**browser_options, proxy=proxy_server, timeout=60000)

            # 创建上下文
            viewport = self.human_simulator.get_random_viewport()
            context = await browser.new_context(
                viewport=viewport,
                user_agent=random.choice(user_agents)
            )

            # 禁用WebDriver
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });

                // 隐藏自动化特征
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({state: Notification.permission}) :
                        originalQuery(parameters)
                );

                // 模拟插件
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5].map(() => ({
                        0: {
                            type: "application/x-google-chrome-pdf",
                            suffixes: "pdf",
                            description: "Portable Document Format"
                        },
                        name: "Chrome PDF Plugin",
                        filename: "internal-pdf-viewer",
                        description: "Portable Document Format",
                        length: 1
                    }))
                });
            """)

            # 创建新页面
            page = await context.new_page()

            try:
                # 执行搜索操作
                search_success = await self._perform_search(page, search_query)

                if not search_success:
                    logging.warning("搜索操作失败，可能被检测为机器人")
                    return {}

                # 模拟随机互动
                await self.human_simulator.random_page_interaction(page)

                # 获取cookies
                cookies = await context.cookies()
                formatted_cookies = {}

                # 格式化cookies为字典
                for cookie in cookies:
                    if cookie.get('domain', '').endswith('google.com'):
                        formatted_cookies[cookie['name']] = cookie['value']

                # 更新cookie
                logging.info(f"获取新的cookies: {formatted_cookies}")
                self.update_cookies(formatted_cookies)
                logging.info("Cookies fetched successfully!")

                # 返回cookies
                return formatted_cookies

            except Exception as e:
                logging.error(f"Error fetching cookies: {e}")
                return {}

            finally:
                await browser.close()

    def generate_random_search_query(self):
        """生成随机搜索查询语句"""
        if FAKER_AVAILABLE:
            try:
                # 使用 faker 生成多样化的查询，只使用确实存在的方法
                query_types = [
                    lambda: f"how to learn {fake.word()}",
                    lambda: f"best {fake.word()} {fake.word()}",
                    lambda: f"what is {fake.word()}",
                    lambda: f"{fake.company()} news",
                    lambda: f"{fake.city()} {fake.word()}",
                    lambda: f"{fake.word()} {fake.word()} near me",
                    lambda: f"learn {fake.word()}",
                    lambda: f"{fake.color_name()} {fake.word()}",
                    lambda: fake.sentence(nb_words=3).replace('.', ''),
                    lambda: f"{fake.word()} {fake.word()}",
                    lambda: f"top {random.randint(5, 20)} {fake.word()}",
                    lambda: f"{fake.word()} {fake.word()} guide",
                    lambda: f"{fake.word()} tutorial",
                    lambda: f"{fake.word()} reviews",
                    lambda: f"{fake.first_name()} {fake.word()}",
                    lambda: f"{fake.job()} tips",
                    lambda: f"{fake.country()} travel",
                    lambda: f"cheap {fake.word()}",
                    lambda: f"free {fake.word()}",
                    lambda: f"online {fake.word()}"
                ]
                return random.choice(query_types)()
            except Exception as e:
                # 如果 faker 出错，使用备用查询
                pass

        # 备用查询列表（如果 faker 不可用或出错）
        backup_queries = [
            "what is LLM", "news headlines", "best restaurants near me",
            "how to learn python", "upcoming movies", "stock market news",
            "healthy recipes", "travel destinations", "latest technology trends",
            "home workout routines", "best coffee shops", "popular music tracks",
            "best places to visit", "chinese food", "japanese food",
            "italian food", "how to learn japanese", "how to learn chinese",
            "agentic rag", "llm agent", "what is KG", "graphrag", "lightrag",
            "machine learning", "artificial intelligence", "data science",
            "web development", "mobile apps", "cloud computing",
            "cybersecurity", "blockchain", "cryptocurrency", "startup ideas"
        ]
        return random.choice(backup_queries)

    def create_cookies(self):
        """获取有效的cookies，如果无效则重新获取"""
        # 生成随机搜索查询
        search_query = self.generate_random_search_query()

        # 获取新的cookies
        cookies = asyncio.run(self.fetch_new_cookies(search_query))
        return cookies

if __name__ == "__main__":
    cookie_manager = GoogleCookie()
    cookies = cookie_manager.create_cookies()
    print(cookies)