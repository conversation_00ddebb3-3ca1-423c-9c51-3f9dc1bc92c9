# -*- coding: utf-8 -*-
"""
Database initialization for business search
Author: ji<PERSON> wei
Create Time: 2025/9/1
File Name: init_db.py
"""
import os

from dotenv import load_dotenv
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from business_search.db.db_models import Base

load_dotenv()

# 数据库配置
mysql_host = os.getenv('MYSQL_HOST', '************')
mysql_port = os.getenv('MYSQL_PORT', '13306')
mysql_user = os.getenv('MYSQL_USER', 'root')
mysql_password = os.getenv('MYSQL_PASSWORD', 'qwer1234')
mysql_db = os.getenv('MYSQL_DATABASE', 'business')
# 创建正式的数据库连接
DATABASE_URL = f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_db}"
print(DATABASE_URL)
# 创建数据库引擎
engine = create_engine(
    DATABASE_URL,
    pool_recycle=1800,
    pool_pre_ping=True,
    pool_size=5,
    max_overflow=10,
    echo=False  # 设置为True可以看到SQL语句
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=True, bind=engine, expire_on_commit=False)


def create_tables():
    """创建所有表"""
    Base.metadata.create_all(bind=engine)
    print("数据库表创建完成")


def get_db_session():
    """获取数据库会话"""
    return SessionLocal()


def init_database():
    """初始化数据库"""
    create_tables()


if __name__ == "__main__":
    init_database()
