#!/bin/bash

# Clean browser processes and memory
pids=$(ps -ef | grep -E 'chromium|chrome|headless-shell|playwright' | grep -v grep | awk '{print $2}')

if [ -n "$pids" ]; then
    echo "Cleaning $(echo $pids | wc -w) browser processes..."
    kill -15 $pids 2>/dev/null
    sleep 2

    # Force stop remaining processes
    remaining=$(ps -ef | grep -E 'chromium|chrome|headless-shell|playwright' | grep -v grep | awk '{print $2}')
    if [ -n "$remaining" ]; then
        kill -9 $remaining 2>/dev/null
    fi
    echo "Browser cleanup completed"
fi

# Clean temp files silently
find /tmp -name 'playwright*' -exec rm -rf {} \; 2>/dev/null
