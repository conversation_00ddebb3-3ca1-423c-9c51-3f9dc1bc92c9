# -*- coding: utf-8 -*-
"""
检查数据库外键状态的脚本
Author: AI Assistant
Create Time: 2025/9/8
File Name: check_foreign_keys.py
"""

from sqlalchemy import create_engine, text, inspect
import os
from dotenv import load_dotenv

load_dotenv()

# 数据库配置
mysql_host = os.getenv('MYSQL_HOST', '************')
mysql_port = os.getenv('MYSQL_PORT', '13306')
mysql_user = os.getenv('MYSQL_USER', 'root')
mysql_password = os.getenv('MYSQL_PASSWORD', 'qwer1234')
mysql_db = os.getenv('MYSQL_DATABASE', 'phone_email')

# 创建数据库连接
DATABASE_URL = f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_db}"

engine = create_engine(
    DATABASE_URL,
    pool_recycle=1800,
    pool_pre_ping=True,
    echo=False
)


def check_foreign_keys():
    """检查数据库中的外键约束"""
    print("🔍 检查数据库外键约束状态")
    print("=" * 60)
    
    try:
        inspector = inspect(engine)
        tables = ['search_tasks', 'phone_results', 'email_results']
        
        total_fks = 0
        
        for table_name in tables:
            print(f"\n📋 表: {table_name}")
            try:
                fks = inspector.get_foreign_keys(table_name)
                if fks:
                    print(f"   发现 {len(fks)} 个外键约束:")
                    for fk in fks:
                        total_fks += 1
                        print(f"   - 约束名: {fk['name']}")
                        print(f"     本地列: {fk['constrained_columns']}")
                        print(f"     引用表: {fk['referred_table']}")
                        print(f"     引用列: {fk['referred_columns']}")
                else:
                    print("   ✅ 没有外键约束")
            except Exception as e:
                print(f"   ❌ 检查失败: {e}")
        
        print(f"\n" + "=" * 60)
        if total_fks == 0:
            print("🎉 恭喜！数据库中没有任何外键约束")
            print("✅ 现在可以自由删除数据而不受外键限制")
        else:
            print(f"⚠️  发现 {total_fks} 个外键约束")
            print("💡 建议运行 remove_foreign_keys.py 来删除外键约束")
        print("=" * 60)
        
        return total_fks == 0
        
    except Exception as e:
        print(f"❌ 检查外键时出错: {e}")
        return False


def check_table_structure():
    """检查表结构（列信息）"""
    print("\n📊 检查表结构信息")
    print("-" * 40)
    
    try:
        with engine.connect() as conn:
            tables = ['search_tasks', 'phone_results', 'email_results']
            
            for table_name in tables:
                print(f"\n📋 {table_name} 表结构:")
                result = conn.execute(text(f"DESCRIBE {table_name}"))
                for row in result:
                    field_name = row[0]
                    field_type = row[1]
                    is_null = row[2]
                    key_info = row[3]
                    default_val = row[4]
                    
                    key_str = ""
                    if key_info == "PRI":
                        key_str = " [主键]"
                    elif key_info == "MUL":
                        key_str = " [索引]"
                    
                    print(f"   {field_name}: {field_type}{key_str}")
                    
    except Exception as e:
        print(f"❌ 检查表结构时出错: {e}")


def main():
    """主函数"""
    print("数据库外键检查工具")
    print(f"目标数据库: {mysql_db}")
    
    try:
        # 测试连接
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("✅ 数据库连接成功\n")
        
        # 检查外键
        no_foreign_keys = check_foreign_keys()
        
        # 检查表结构
        check_table_structure()
        
        if no_foreign_keys:
            print("\n🎯 结论: 数据库已成功移除所有外键约束！")
        else:
            print("\n⚠️  结论: 数据库中仍存在外键约束")
            
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")


if __name__ == "__main__":
    main()
