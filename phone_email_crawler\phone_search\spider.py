# -*- coding: utf-8 -*-
"""
Phone search spider for phone and email crawler
Author: ji<PERSON> <PERSON>i
Create Time: 2025/8/18
File Name: spider.py
"""
import random
import time
import re
import logging
import threading
from datetime import datetime
from typing import List, Tuple, Optional, Dict

import requests
from lxml import etree
from sqlalchemy import func

import sys
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from phone_email_crawler.shared.cookies import GoogleCookie
from phone_email_crawler.shared.proxy import GlobalProxyPool
from phone_email_crawler.shared.db.init_db import get_db_session
from phone_email_crawler.shared.db.db_models import SearchTasks, PhoneResults
from phone_email_crawler.phone_search.config import PHONE_SEARCH_CONFIG, USER_AGENTS, BASE_HEADERS

# 配置日志
logging.basicConfig(
    level=getattr(logging, PHONE_SEARCH_CONFIG['log_level']),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(PHONE_SEARCH_CONFIG['log_file']),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('PhoneSearch')


class PhoneSearchSpider:
    """电话搜索爬虫"""
    
    def __init__(self, thread_id=0):
        self.thread_id = thread_id

        # Cookie管理
        cookie_file = f"cookies/{PHONE_SEARCH_CONFIG['cookie_prefix']}{thread_id}.json"
        self.google_cookie = GoogleCookie(cookie_file=cookie_file)

        # 全局代理池
        self.global_proxy_pool = GlobalProxyPool()
        #打印global_proxy_pool的id
        logger.info(f"[线程 {self.thread_id}] 全局代理池id: {id(self.global_proxy_pool)}")
        # 当前代理管理
        self.current_proxy = None
        self.proxy_usage_count = 0
        self.proxy_error_count = 0
        self.max_proxy_usage = PHONE_SEARCH_CONFIG['max_proxy_usage']
        self.max_errors_before_proxy_change = PHONE_SEARCH_CONFIG['max_errors_before_proxy_change']

        # 重试机制参数
        self.cookie_failure_count = 0
        self.max_cookie_failures = PHONE_SEARCH_CONFIG['max_cookie_failures']

        # 线程锁
        self.lock = threading.Lock()

        logger.info(f"[线程 {self.thread_id}] 电话搜索爬虫初始化完成")
    
    def get_headers(self) -> Dict[str, str]:
        """生成带有随机User-Agent的请求头"""
        headers = BASE_HEADERS.copy()
        headers['user-agent'] = random.choice(USER_AGENTS)
        return headers
    
    def extract_phones(self, content: str) -> List[str]:
        """从内容中提取所有美国电话号码，保持原始格式"""
        if not content:
            return []

        found_phones = []

        # 电话号码正则表达式模式（保持原始格式）
        phone_patterns = [
            # 格式1: ( ************* (括号后直接跟连字符)
            r'\(\s*\d{3}\s*\)\s*-\s*\d{3}\s*-\s*\d{4}',
            # 格式2: (************* 或 ( ************* (括号后有空格)
            r'\(\s*\d{3}\s*\)\s+\d{3}\s*-\s*\d{4}',
            # 格式3: (304)529-7961 (括号后无空格)
            r'\(\s*\d{3}\s*\)\s*\d{3}\s*-\s*\d{4}',
            # 格式4: ************ (标准连字符格式)
            r'(?<!\d)\d{3}\s*-\s*\d{3}\s*-\s*\d{4}(?!\d)',
            # 格式5: ************ (空格+连字符混合)
            r'(?<!\d)\d{3}\s+\d{3}\s*-\s*\d{4}(?!\d)',
            # 格式6: ************ (点分格式)
            r'(?<!\d)\d{3}\s*\.\s*\d{3}\s*\.\s*\d{4}(?!\d)',
            # 格式7: ************ (空格分隔)
            r'(?<!\d)\d{3}\s+\d{3}\s+\d{4}(?!\d)',
            # 格式8: 3042623474 (10位连续数字)
            r'(?<!\d)\d{10}(?!\d)',
            # 格式9: ****** 262 3474 (美国国际格式)
            r'\+1\s*\d{3}\s*\d{3}\s*\d{4}',
            # 格式10: ************** (带国家代码)
            r'1\s*-\s*\d{3}\s*-\s*\d{3}\s*-\s*\d{4}',
            # 格式11: (+995) 322 401 111 (国际格式，括号内国家代码)
            r'\(\s*\+\d{1,4}\s*\)\s+\d{2,4}\s+\d{2,4}\s+\d{2,4}',
            # 格式12: +995 322 401 111 (国际格式，无括号)
            r'\+\d{1,4}\s+\d{2,4}\s+\d{2,4}\s+\d{2,4}',
        ]

        for pattern in phone_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                # 清理匹配结果，去除多余空格但保持原始格式
                phone = re.sub(r'\s+', ' ', match.strip())
                if phone and phone not in found_phones:
                    found_phones.append(phone)

        return found_phones
    
    def check_cookies_valid(self, page) -> bool:
        """检查页面是否有有效的搜索结果（Cookie是否有效）"""
        try:
            # 检查是否有搜索结果
            h3_elements = page.xpath('//h3')
            return len(h3_elements) >= 1
        except Exception:
            return False
    
    def increment_cookie_failure(self) -> bool:
        """增加Cookie失败计数，如果达到阈值则重置Cookie"""
        self.cookie_failure_count += 1
        logger.warning(f"[线程 {self.thread_id}] Cookie失败计数: {self.cookie_failure_count}/{self.max_cookie_failures}")
        
        if self.cookie_failure_count >= self.max_cookie_failures:
            self.reset_cookie()
            return True
        return False
    
    def reset_cookie(self):
        """重置Cookie并重置失败计数器"""
        max_retries = PHONE_SEARCH_CONFIG['cookie_reset_retries']
        retry_delay = PHONE_SEARCH_CONFIG['cookie_reset_delay']
        
        try:
            with self.lock:
                logger.info(f"[线程 {self.thread_id}] Cookie累计失败{self.cookie_failure_count}次，重新生成Cookie")
                
                for attempt in range(max_retries):
                    try:
                        logger.info(f"[线程 {self.thread_id}] 尝试获取新Cookie (第{attempt + 1}/{max_retries}次)")
                        new_cookies = self.google_cookie.create_cookies()
                        
                        if new_cookies and self.google_cookie.is_cookies_valid(new_cookies):
                            self.google_cookie.cookies = new_cookies
                            self.cookie_failure_count = 0
                            logger.info(f"[线程 {self.thread_id}] 新Cookie已生成")
                            return
                        else:
                            logger.warning(f"[线程 {self.thread_id}] 第{attempt + 1}次获取的Cookie无效")
                            if attempt < max_retries - 1:
                                time.sleep(retry_delay)
                    
                    except Exception as retry_e:
                        logger.error(f"[线程 {self.thread_id}] 第{attempt + 1}次获取Cookie时出错: {retry_e}")
                        if attempt < max_retries - 1:
                            time.sleep(retry_delay)
                
                logger.error(f"[线程 {self.thread_id}] 经过{max_retries}次尝试，仍无法获取有效Cookie")
        
        except Exception as e:
            logger.error(f"[线程 {self.thread_id}] 重置Cookie失败: {e}")

    def get_current_proxy(self) -> Optional[str]:
        """获取当前可用的代理"""
        # 检查是否需要更换代理
        need_new_proxy = (
            self.current_proxy is None or  # 没有代理
            self.proxy_usage_count >= self.max_proxy_usage or  # 使用次数达到上限
            self.proxy_error_count >= self.max_errors_before_proxy_change  # 错误次数达到上限
        )

        if need_new_proxy:
            # 获取新代理
            new_proxy = self.global_proxy_pool.get_proxy()
            if new_proxy:
                self.current_proxy = new_proxy
                self.proxy_usage_count = 0
                self.proxy_error_count = 0
                logger.info(f"[线程 {self.thread_id}] 更换新代理: {new_proxy}")
            else:
                logger.warning(f"[线程 {self.thread_id}] 无法获取新代理")
                return None

        return self.current_proxy

    def record_proxy_usage(self, success: bool):
        """记录代理使用情况"""
        if self.current_proxy:
            self.proxy_usage_count += 1
            if not success:
                self.proxy_error_count += 1
                logger.debug(f"[线程 {self.thread_id}] 代理错误计数: {self.proxy_error_count}/{self.max_errors_before_proxy_change}")
            else:
                # 成功时重置错误计数
                self.proxy_error_count = 0

    def search_phone_page(self, search_query: str, page_num: int = 0,
                         max_retries: int = 2) -> Dict:
        """搜索特定页码的电话号码结果"""
        
        # 搜索参数
        params = {
            'q': search_query,
            'oq': search_query,
            'start': page_num * 10,
            'gs_lcrp': '',
            'sourceid': 'chrome',
            'ie': 'UTF-8',
        }
        
        # 获取当前代理
        proxy = self.get_current_proxy()
        proxy_server = None
        if proxy:
            proxy_server = {
                "http": f"http://{proxy}",
                "https": f"http://{proxy}"
            }
        
        for attempt in range(max_retries):
            try:
                cookies = self.google_cookie.cookies

                # 确保Cookie不为空
                if not cookies:
                    logger.warning(f"[线程 {self.thread_id}] Cookie为空，尝试重新生成")
                    self.reset_cookie()
                    cookies = self.google_cookie.cookies
                    if not cookies:
                        logger.error(f"[线程 {self.thread_id}] 重新生成Cookie失败，跳过此次请求")
                        self.record_proxy_usage(False)
                        continue

                headers = self.get_headers()

                response = requests.get(
                    url='https://www.google.com/search',
                    headers=headers,
                    cookies=cookies,
                    params=params,
                    timeout=PHONE_SEARCH_CONFIG['request_timeout'],
                    proxies=proxy_server
                )
                
                if response.status_code == 200:
                    page = etree.HTML(response.text)
                    
                    # 检查Cookie是否有效
                    if not self.check_cookies_valid(page):
                        logger.info(f"[线程 {self.thread_id}] 页面没有有效结果，Cookie可能失效")
                        self.increment_cookie_failure()
                        self.record_proxy_usage(False)  # 记录代理使用失败
                        continue
                    
                    # 提取搜索结果
                    url_elements = page.xpath('//a[@jsname="UWckNb"]/@href')
                    
                    # 获取URL和内容
                    urls = [url for url in url_elements]
                    contents = []
                    for i in range(len(url_elements)):
                        content_xpath = f'(//div[@jscontroller="SC7lYd"]//*[@data-snf="nke7rc"])[{i + 1}]//text()'
                        content_texts = page.xpath(content_xpath)
                        content = ' '.join(content_texts)
                        contents.append(content)
                    
                    # 提取电话号码结果
                    results = []
                    for i, url in enumerate(urls):
                        try:
                            content = contents[i] if i < len(contents) else ''
                            phones = self.extract_phones(content)
                            for phone in phones:
                                results.append((url, phone))
                        except Exception as e:
                            logger.error(f"[线程 {self.thread_id}] 提取结果 {i + 1} 时出错: {e}")
                    
                    # 检查是否有下一页
                    has_next = page.xpath('//a[@id="pnnext"]')

                    # 记录代理使用成功
                    self.record_proxy_usage(True)

                    return {
                        'results': results,
                        'has_next': len(has_next) > 0,
                        'success': True
                    }
                
                elif response.status_code == 429:
                    logger.warning(f"[线程 {self.thread_id}] 请求被限制 (429)，Cookie可能失效")
                    self.increment_cookie_failure()
                    self.record_proxy_usage(False)

                else:
                    logger.warning(f"[线程 {self.thread_id}] 请求返回状态码: {response.status_code}")
                    self.increment_cookie_failure()
                    self.record_proxy_usage(False)

            except Exception as e:
                logger.error(f'[线程 {self.thread_id}] 请求错误: {e}, 尝试第 {attempt + 1}/{max_retries} 次')
                self.increment_cookie_failure()
                self.record_proxy_usage(False)

        logger.error(f"[线程 {self.thread_id}] 所有 {max_retries} 次尝试均失败")
        return {
            'results': [],
            'has_next': False,
            'success': False
        }

    def search_phones(self, keyword: str, region: str) -> Tuple[List[Tuple[int, str, str]], int]:
        """搜索电话号码，爬取到最后一页，返回(结果列表, 总页数)"""
        search_query = f"{keyword} phone {region}"
        logger.info(f"[线程 {self.thread_id}] 开始搜索: {search_query}")

        all_results = []
        page_num = 0
        has_next = True

        while has_next:
            # 搜索当前页
            page_result = self.search_phone_page(
                search_query, page_num,
                PHONE_SEARCH_CONFIG['max_retries']
            )

            # 检查是否有结果
            if not page_result['results'] and not page_result['has_next']:
                logger.error(f"[线程 {self.thread_id}] 获取第 {page_num + 1} 页失败，停止搜索")
                break

            # 添加当前页面的结果
            for url, phone in page_result['results']:
                all_results.append((page_num + 1, url, phone))

            has_next = page_result['has_next']
            page_num += 1

            # 页面间延迟
            if has_next:
                delay = random.uniform(
                    PHONE_SEARCH_CONFIG['request_delay_min'],
                    PHONE_SEARCH_CONFIG['request_delay_max']
                )
                time.sleep(delay)

        logger.info(f"[线程 {self.thread_id}] 完成搜索 '{search_query}'，共爬取 {page_num} 页，找到 {len(all_results)} 个电话")
        return all_results, page_num


class PhoneSearchThreadManager:
    """电话搜索线程管理器"""

    def __init__(self, num_threads: int = None, batch_size: int = None):
        self.num_threads = num_threads or PHONE_SEARCH_CONFIG['threads']
        self.batch_size = batch_size or PHONE_SEARCH_CONFIG['batch_size']
        self.save_frequency = PHONE_SEARCH_CONFIG['save_frequency']

        self.result_lock = threading.Lock()
        self.threads = []
        self.completed_queries = 0

        logger.info(f"电话搜索管理器初始化: {self.num_threads} 个线程, 批处理大小 {self.batch_size}")

    def get_pending_phone_tasks(self, batch_size: int = 10) -> List[SearchTasks]:
        """获取待处理的电话搜索任务"""
        db = get_db_session()
        try:
            tasks = db.query(SearchTasks).filter(
                SearchTasks.search_phone_status == 'pending'
            ).order_by(func.random()).limit(batch_size).all()

            logger.info(f"获取到 {len(tasks)} 个待处理的电话搜索任务")
            return tasks
        except Exception as e:
            logger.error(f"获取电话搜索任务失败: {e}")
            return []
        finally:
            db.close()

    def save_phone_results(self, task_id: int, keyword: str, region: str,
                          search_query: str, results: List[Tuple[int, str, str]],
                          total_pages: int):
        """保存电话搜索结果到数据库"""
        db = get_db_session()
        try:
            phone_results = []
            for page_num, url, phone in results:
                phone_result = PhoneResults(
                    task_id=task_id,
                    keyword=keyword,
                    region=region,
                    search_query=search_query,
                    url=url,
                    phone_number=phone,
                    current_page=page_num
                )
                phone_results.append(phone_result)

            if phone_results:
                db.add_all(phone_results)
                db.commit()
                logger.info(f"保存了 {len(phone_results)} 个电话搜索结果")

            # 更新任务状态逻辑：只有当页数和结果数同时为0时才不更新状态
            task = db.query(SearchTasks).filter(SearchTasks.id == task_id).first()
            if task:
                if total_pages == 0 and len(results) == 0:
                    logger.info(f"[任务 {task_id}] 页数和电话数量都是0，不更新search_phone_status（可能是爬虫失败）")
                    # 跳过状态更新，保持原有状态
                else:
                    # 只有当有页数或有结果时才标记为completed
                    task.search_phone_status = 'completed'
                    db.commit()
                    logger.info(f"任务 {task_id} 电话搜索状态已更新为completed")

        except Exception as e:
            logger.error(f"保存电话搜索结果失败: {e}")
            db.rollback()
        finally:
            db.close()

    def batch_save_phone_results(self, batch_results: List[Dict]):
        """批量保存电话搜索结果到数据库"""
        if not batch_results:
            return

        db = get_db_session()
        try:
            all_phone_results = []
            task_updates = []

            for batch_item in batch_results:
                task_id = batch_item['task_id']
                keyword = batch_item['keyword']
                region = batch_item['region']
                search_query = batch_item['search_query']
                results = batch_item['results']
                total_pages = batch_item['total_pages']

                # 收集电话结果
                for page_num, url, phone in results:
                    phone_result = PhoneResults(
                        task_id=task_id,
                        keyword=keyword,
                        region=region,
                        search_query=search_query,
                        url=url,
                        phone_number=phone,
                        current_page=page_num
                    )
                    all_phone_results.append(phone_result)

                # 收集任务状态更新
                if not (total_pages == 0 and len(results) == 0):
                    task_updates.append(task_id)

            # 批量插入电话结果
            if all_phone_results:
                db.add_all(all_phone_results)
                logger.info(f"批量保存了 {len(all_phone_results)} 个电话搜索结果")

            # 批量更新任务状态
            if task_updates:
                db.query(SearchTasks).filter(SearchTasks.id.in_(task_updates)).update(
                    {SearchTasks.search_phone_status: 'completed'},
                    synchronize_session=False
                )
                logger.info(f"批量更新了 {len(task_updates)} 个任务的电话搜索状态")

            db.commit()

        except Exception as e:
            logger.error(f"批量保存电话搜索结果失败: {e}")
            db.rollback()
        finally:
            db.close()

    def phone_worker(self, thread_id: int, tasks: List[SearchTasks]):
        """电话搜索工作线程"""
        if not tasks:
            logger.info(f"[线程 {thread_id}] 没有分配到任务")
            return

        spider = PhoneSearchSpider(thread_id=thread_id)
        completed = 0

        # 批量保存相关变量
        batch_results = []
        batch_count = 0

        for task in tasks:
            try:
                logger.info(f"[线程 {thread_id}] 开始处理任务 {task.id}: {task.keyword} + {task.region}")

                # 执行电话搜索
                results, total_pages = spider.search_phones(task.keyword, task.region)

                # 收集结果到批次中
                search_query = f"{task.keyword} phone {task.region}"
                batch_results.append({
                    'task_id': task.id,
                    'keyword': task.keyword,
                    'region': task.region,
                    'search_query': search_query,
                    'results': results,
                    'total_pages': total_pages
                })

                batch_count += 1
                completed += 1

                if results:
                    logger.info(f"[线程 {thread_id}] 任务 {task.id} 完成，爬取 {total_pages} 页，找到 {len(results)} 个电话")
                else:
                    logger.warning(f"[线程 {thread_id}] 任务 {task.id} 爬取 {total_pages} 页，未找到电话")

                # 达到保存频率时批量保存
                if batch_count >= self.save_frequency:
                    self.batch_save_phone_results(batch_results)
                    batch_results = []
                    batch_count = 0
                    logger.info(f"[线程 {thread_id}] 批量保存了 {self.save_frequency} 个任务的结果")

                # 添加任务间延迟
                delay = random.uniform(1.0, 3.0)
                time.sleep(delay)

            except Exception as e:
                logger.error(f"[线程 {thread_id}] 处理任务 {task.id} 时出错: {e}")
                continue

        # 保存剩余的结果
        if batch_results:
            self.batch_save_phone_results(batch_results)
            logger.info(f"[线程 {thread_id}] 最终批量保存了 {len(batch_results)} 个任务的结果")

        with self.result_lock:
            self.completed_queries += completed

        logger.info(f"[线程 {thread_id}] 完成 {completed} 个任务")

    def start_batch_processing(self, batch_size: int = None) -> bool:
        """启动批处理模式"""
        batch_size = batch_size or self.batch_size

        # 获取待处理任务
        tasks = self.get_pending_phone_tasks(batch_size)
        if not tasks:
            logger.info("没有待处理的电话搜索任务")
            return False

        # 分配任务给线程
        tasks_per_thread = len(tasks) // self.num_threads
        remainder = len(tasks) % self.num_threads

        self.threads = []
        start_idx = 0

        for i in range(self.num_threads):
            # 计算当前线程的任务数量
            current_tasks_count = tasks_per_thread + (1 if i < remainder else 0)
            if current_tasks_count == 0:
                break

            # 分配任务
            thread_tasks = tasks[start_idx:start_idx + current_tasks_count]
            start_idx += current_tasks_count

            # 创建并启动线程
            thread = threading.Thread(
                target=self.phone_worker,
                args=(i, thread_tasks)
            )
            thread.daemon = True
            self.threads.append(thread)
            thread.start()

            logger.info(f"线程 {i} 已启动，处理 {len(thread_tasks)} 个任务")

        logger.info(f"已启动 {len(self.threads)} 个电话搜索线程")
        return True

    def wait_completion(self):
        """等待所有线程完成"""
        for thread in self.threads:
            thread.join()
        logger.info("所有电话搜索线程已完成")


# 测试电话提取功能
if __name__ == "__main__":
    spider = PhoneSearchSpider()

    # 测试用例1：原始测试内容
    test_content1 = """
    Our Head Chef · Read About Us · Check Out Our Reviews on TripAdvisor · Clams · Oysters · Lobster · Salmon · Cod · & Much More! Contact. Phone ( ************* .
    You can text your order now to  ************  Or call ************ Fri & Sat during business hours (11-7) Prices for Aug 15 & 16 Reg Males $25/doz $65 1/2 bush
    Fresh Seafood Company ; Website: http://www.capitolmarket.net/ ; Phone: ( *************  ; Location: 800 Smith St., Charleston, WV, 25301
    1121 Quarrier St Charleston, WV 25301  ************  Visit Website East End Lunch, Dinner $ Charleston, West Virginia's freshest seafood and finest meats.
    """

    # 测试用例2：新的问题案例
    test_content2 = """
    Find us on... Facebook page, opens in a new tab · Instagram page, opens in a new tab · Google page, opens in a new tab. Contact us. Call us at ( ************* .
    Call us or visit the store!  ************  · 57 E. MAIN STREET CHESTER, NJ. Sunday, 11am - 9pm. Monday, Closed. Tues-Thurs, 11am ...
    """

    # 测试用例3：多种格式和国际号码
    test_content3 = """
    紧急联系：请拨打************或(*************。国际客户请联系******-987-6543。
    技术支持热线：**************** 分机 22。备用号码：************
    付款问题请咨询：************** 或访问网站 www.example.com/contact
    """

    # 测试用例4：多种格式和无效号码
    test_content4 = """
    门店信息：纽约店电话212.555.1212，旧金山店(415)555-1313。
    客服邮箱：<EMAIL> 紧急热线：877 555 1414
    营业时间咨询：1 (************* 或 555-1616（本地）
    注意：无效号码如12345或555-123应被忽略
    """

    # 测试用例5：国际号码和格式变体
    test_content5 = """
    海外联系：+44 20 7123 4567 或 +852 1234 5678
    北美免费电话：18005551234 或 1(900)555-5678
    格式测试：(123)456-7890 | 123-456-7890 | 123.456.7890 | 1234567890
    """

    # 测试用例6：数字混淆和特殊格式
    test_content6 = """
    数字混淆测试：订单号AB12345，价格$299.99，日期2023-09-15
    正确号码应提取：(800)555-6789 和 888.555.3456
    避免误提取：ISBN 978-3-16-148410-0，坐标40.7128°N,74.0060°W
    """

    # 测试用例7：扩展号和边缘情况
    test_content7 = """
    特殊格式：号码可能被括号包围如(555)123-4567或带扩展号555-0000 ext.5050
    连续出现：拨打5551112222或(555)333-4444立即咨询
    空号测试：123-4567（缺区号）和555-123（位数不足）不应提取
    """

    # 测试用例8：多语言环境和嵌入句子
    test_content8 = """
    多语言环境：中文客服电话：400-112-1234，English hotline: (800) 555-7890
    号码嵌入句子：请致电8005552468获取帮助，或联系(555) 789-0123专员
    分隔符测试：555_123_4567（下划线无效）vs 555/123/4567（斜杠无效）
    """

    # 测试用例9：新发现的问题案例
    test_content9 = """
    Find us on... Facebook page, opens in a new tab · Instagram page, opens in a new tab · Google page, opens in a new tab. Contact us. Call us at ( ************* .
    Call us or visit the store!  ************  · 57 E. MAIN STREET CHESTER, NJ. Sunday, 11am - 9pm. Monday, Closed. Tues-Thurs, 11am ...
    Contact us. Email: <EMAIL>.  Phone : (+995) 322 401 111. Address: #3 Aleksandre Pushkin Str. Tbilisi,  Georgia . Solutions. Brokerage: <EMAIL>. Research ...
    Walthourville Meat Market, Hinesville. 29029 likes · 2392 talking about this. Our stores phone is  ************  address is 5715 oglthorpe...
    """

    # 执行所有测试用例
    for i in range(1, 10):
        content = globals()[f"test_content{i}"]
        print(f"\n=== 测试用例{i} ===")
        phones = spider.extract_phones(content)
        print("提取到的电话号码:")
        for phone in phones:
            print(f"  - {phone}")

        # 为测试用例9添加期望结果说明
        if i == 9:
            print("期望结果: ( *************, ************, (+995) 322 401 111, ************")

        # 打印期望结果（根据测试内容预设）
        if i == 1:
            print("期望结果: (*************, (*************, (*************, (*************, (*************")
        elif i == 2:
            print("期望结果: (*************, (*************")
        elif i == 3:
            print(
                "期望结果: (*************, (*************, (*************, (*************, (*************, (*************")
        elif i == 4:
            print("期望结果: (*************, (*************, (*************, (*************")
        elif i == 5:
            print("期望结果: (*************, (*************, (*************, (*************")
        elif i == 6:
            print("期望结果: (*************, (*************")
        elif i == 7:
            print("期望结果: (*************, (555) 333-4444")
        elif i == 8:
            print("期望结果: (400) 112-1234, (800) 555-7890, (555) 789-0123")
