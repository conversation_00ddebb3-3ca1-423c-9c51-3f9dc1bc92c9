#!/bin/bash

# Business Crawler Stopper
# Author: ji<PERSON> <PERSON>i
# Create Time: 2025/9/3
# File Name: stop_business_crawler.sh
# Description: Stops business search crawler processes

PROJECT_DIR="/root/google-search/business_search"
PID_FILE="$PROJECT_DIR/logs/business_crawler.pid"
LOG_FILE="$PROJECT_DIR/logs/business_cycle.log"

# Function to log messages with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

echo "🏢 停止业务爬虫进程..."

# 首先显示当前运行的相关进程
echo "🔍 当前运行的相关进程:"
ps aux | grep -E "(main\.py|business)" | grep -v grep | while read line; do
    echo "   $line"
done

# Stop using PID file first
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        echo "⏹️ 停止业务爬虫循环脚本 (PID: $PID)"
        kill -TERM "$PID" 2>/dev/null
        sleep 3
        if kill -0 "$PID" 2>/dev/null; then
            echo "🔨 强制停止业务爬虫循环脚本"
            kill -KILL "$PID" 2>/dev/null
        fi
        log_message "🛑 业务爬虫循环脚本已停止"
    fi
    rm -f "$PID_FILE"
else
    echo "⚠️ 未找到PID文件，通过进程名查找并停止..."
fi

# Kill business crawler main.py processes
echo "🔍 查找并停止业务爬虫主程序..."
BUSINESS_PIDS=$(ps aux | grep "main.py" | grep -v grep | awk '{print $2}')
if [ ! -z "$BUSINESS_PIDS" ]; then
    echo "🏢 发现业务爬虫主程序进程: $BUSINESS_PIDS"
    for pid in $BUSINESS_PIDS; do
        echo "⏹️ 停止进程 PID: $pid"
        kill -TERM $pid 2>/dev/null
    done
    sleep 3

    # 检查是否还有残留进程
    REMAINING_PIDS=$(ps aux | grep "main.py" | grep -v grep | awk '{print $2}')
    if [ ! -z "$REMAINING_PIDS" ]; then
        echo "🔨 强制停止残留的业务爬虫进程..."
        for pid in $REMAINING_PIDS; do
            echo "🔨 强制停止进程 PID: $pid"
            kill -KILL $pid 2>/dev/null
        done
    fi
    log_message "🛑 业务爬虫主程序已停止"
else
    echo "✅ 未发现运行中的业务爬虫主程序 (main.py)"
fi

# Kill start_business_crawler.sh processes
echo "🔍 查找并停止业务爬虫启动脚本..."
START_PIDS=$(ps aux | grep "start_business_crawler.sh" | grep -v grep | grep -v $$ | awk '{print $2}')
if [ ! -z "$START_PIDS" ]; then
    echo "📜 停止业务爬虫启动脚本进程: $START_PIDS"
    echo "$START_PIDS" | xargs -r kill -TERM 2>/dev/null
    sleep 2
    # Force kill if still running
    REMAINING_START_PIDS=$(ps aux | grep "start_business_crawler.sh" | grep -v grep | grep -v $$ | awk '{print $2}')
    if [ ! -z "$REMAINING_START_PIDS" ]; then
        echo "🔨 强制停止残留的启动脚本进程: $REMAINING_START_PIDS"
        echo "$REMAINING_START_PIDS" | xargs -r kill -KILL 2>/dev/null
    fi
    log_message "🛑 业务爬虫启动脚本已停止"
else
    echo "✅ 未发现运行中的业务爬虫启动脚本"
fi

# Optional: Clean up browser processes
CLEANUP_SCRIPT="$PROJECT_DIR/sh/cleanup_browsers.sh"
if [ -f "$CLEANUP_SCRIPT" ]; then
    echo "🧹 清理浏览器进程..."
    bash "$CLEANUP_SCRIPT"
fi

# 最终验证是否还有残留进程
echo "🔍 最终检查是否还有残留进程..."
FINAL_CHECK=$(ps aux | grep -E "(main\.py|business)" | grep -v grep)
if [ ! -z "$FINAL_CHECK" ]; then
    echo "⚠️ 发现残留进程:"
    echo "$FINAL_CHECK"
    echo "❌ 可能需要手动清理"
else
    echo "✅ 确认所有相关进程已停止"
fi

echo "✅ 业务爬虫停止完成！"
log_message "✅ 业务爬虫完全停止"
