#!/bin/bash

# Email Crawler Stopper
# Author: ji<PERSON> <PERSON>i
# Create Time: 2025/8/18
# File Name: stop_email_crawler.sh
# Description: Stops email search crawler processes

PROJECT_DIR="/root/google-search/phone_email_crawler"
PID_FILE="$PROJECT_DIR/email_search/logs/email_crawler.pid"
LOG_FILE="$PROJECT_DIR/email_search/logs/email_cycle.log"

# Function to log messages with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

echo "📧 停止邮箱爬虫进程..."

# Stop using PID file first
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        echo "⏹️ 停止邮箱爬虫循环脚本 (PID: $PID)"
        kill -TERM "$PID" 2>/dev/null
        sleep 3
        if kill -0 "$PID" 2>/dev/null; then
            echo "🔨 强制停止邮箱爬虫循环脚本"
            kill -KILL "$PID" 2>/dev/null
        fi
        log_message "🛑 邮箱爬虫循环脚本已停止"
    fi
    rm -f "$PID_FILE"
else
    echo "⚠️ 未找到PID文件，尝试通过进程名停止..."
fi

# Kill email crawler main.py processes
echo "🔍 查找并停止邮箱爬虫主程序..."
EMAIL_PIDS=$(ps aux | grep "email_search/main.py" | grep -v grep | awk '{print $2}')
if [ ! -z "$EMAIL_PIDS" ]; then
    echo "📧 停止邮箱爬虫主程序进程: $EMAIL_PIDS"
    echo "$EMAIL_PIDS" | xargs -r kill -TERM 2>/dev/null
    sleep 3
    # Force kill if still running
    REMAINING_PIDS=$(ps aux | grep "email_search/main.py" | grep -v grep | awk '{print $2}')
    if [ ! -z "$REMAINING_PIDS" ]; then
        echo "🔨 强制停止残留的邮箱爬虫进程: $REMAINING_PIDS"
        echo "$REMAINING_PIDS" | xargs -r kill -KILL 2>/dev/null
    fi
    log_message "🛑 邮箱爬虫主程序已停止"
else
    echo "✅ 未发现运行中的邮箱爬虫主程序"
fi

# Kill start_email_crawler.sh processes
echo "🔍 查找并停止邮箱爬虫启动脚本..."
START_PIDS=$(ps aux | grep "start_email_crawler.sh" | grep -v grep | grep -v $$ | awk '{print $2}')
if [ ! -z "$START_PIDS" ]; then
    echo "📜 停止邮箱爬虫启动脚本进程: $START_PIDS"
    echo "$START_PIDS" | xargs -r kill -TERM 2>/dev/null
    sleep 2
    # Force kill if still running
    REMAINING_START_PIDS=$(ps aux | grep "start_email_crawler.sh" | grep -v grep | grep -v $$ | awk '{print $2}')
    if [ ! -z "$REMAINING_START_PIDS" ]; then
        echo "🔨 强制停止残留的启动脚本进程: $REMAINING_START_PIDS"
        echo "$REMAINING_START_PIDS" | xargs -r kill -KILL 2>/dev/null
    fi
    log_message "🛑 邮箱爬虫启动脚本已停止"
else
    echo "✅ 未发现运行中的邮箱爬虫启动脚本"
fi

# Optional: Clean up browser processes
CLEANUP_SCRIPT="$PROJECT_DIR/sh/cleanup_browsers.sh"
if [ -f "$CLEANUP_SCRIPT" ]; then
    echo "🧹 清理浏览器进程..."
    bash "$CLEANUP_SCRIPT"
fi

echo "✅ 邮箱爬虫停止完成！"
log_message "✅ 邮箱爬虫完全停止"
