# -*- coding: utf-8 -*-
"""
Task generation tool for phone and email crawler
Author: <PERSON><PERSON> <PERSON>i
Create Time: 2025/8/18
File Name: generate_tasks.py
"""
import json
import sys
import os
import logging
from sqlalchemy.exc import IntegrityError
from sqlalchemy import text

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from phone_email_crawler.shared.db.init_db import get_db_session
from phone_email_crawler.shared.db.db_models import SearchTasks

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_keywords():
    """从ebay/关键字.txt加载关键词"""
    keywords_file = '../../ebay/关键字.txt'
    keywords = []
    
    try:
        with open(keywords_file, 'r', encoding='utf-8') as f:
            for line in f:
                keyword = line.strip()
                if keyword:  # 跳过空行
                    keywords.append(keyword)
        
        logger.info(f"成功加载{len(keywords)}个关键词")
        return keywords
    except FileNotFoundError:
        logger.error(f"关键词文件不存在: {keywords_file}")
        return []
    except Exception as e:
        logger.error(f"加载关键词失败: {e}")
        return []


def load_regions():
    """从ebay/county_list.json加载地区信息（优化版本）"""
    county_file = '../../ebay/county_list.json'

    try:
        with open(county_file, 'r', encoding='utf-8') as f:
            county_data = json.load(f)

        # 使用集合去重，提高性能
        state_regions = set()
        county_regions = set()

        for state in county_data:
            # 收集州名
            state_name = state.get('sate_name', '').strip()
            if state_name:
                state_regions.add(state_name)

            # 收集县名（去除County和city后缀，参考export_fast.py）
            county_list = state.get('county_list', [])
            for county in county_list:
                county_name = county.get('county_name', '').strip()
                if county_name:
                    # 去除"County"和"city"后缀，参考export_fast.py的处理方式
                    clean_name = county_name.replace("County", "").replace("city", "").strip()
                    if clean_name:
                        county_regions.add(clean_name)

        # 转换为排序列表
        state_list = sorted(list(state_regions))
        county_list = sorted(list(county_regions))

        # 询问用户选择使用哪种地区数据
        print(f"发现{len(state_list)}个州和{len(county_list)}个县")
        print("请选择要使用的地区数据:")
        print("1. 使用州名 (数据量较小，约50个)")
        print("2. 使用县名 (数据量较大，约3000+个)")
        print("3. 混合使用 (州名+县名)")

        choice = input("请输入选择 (1/2/3，默认2): ").strip()

        if choice == '1':
            regions = state_list
            logger.info(f"选择使用州名，共{len(regions)}个地区")
        elif choice == '3':
            regions = state_list + county_list
            logger.info(f"选择混合使用，共{len(regions)}个地区（{len(state_list)}个州 + {len(county_list)}个县）")
        else:  # 默认选择县名
            regions = county_list
            logger.info(f"选择使用县名，共{len(regions)}个地区")

        return regions

    except FileNotFoundError:
        logger.error(f"地区文件不存在: {county_file}")
        return []
    except Exception as e:
        logger.error(f"加载地区信息失败: {e}")
        return []


def generate_tasks_fast(keywords, regions):
    """使用原生SQL快速生成所有搜索任务"""
    db = get_db_session()
    try:
        total_tasks = len(keywords) * len(regions)
        logger.info(f"准备生成{total_tasks}个搜索任务")

        # 构造所有任务组合
        task_combinations = []
        for keyword in keywords:
            for region in regions:
                task_combinations.append({
                    'keyword': keyword,
                    'region': region
                })

        logger.info(f"构造完成，共{len(task_combinations)}个任务组合")

        # 使用原生SQL批量插入
        batch_size = 2000  # 增大批量大小
        inserted_count = 0

        logger.info("开始使用原生SQL批量插入...")

        for i in range(0, len(task_combinations), batch_size):
            batch = task_combinations[i:i + batch_size]

            # 构建批量插入的SQL
            values = []
            for task in batch:
                # 转义单引号防止SQL注入
                keyword = task['keyword'].replace("'", "''")
                region = task['region'].replace("'", "''")
                values.append(f"('{keyword}', '{region}', 'pending', 'pending', NOW(), NOW())")

            if values:
                # 使用INSERT IGNORE避免重复插入
                insert_sql = f"""
                INSERT IGNORE INTO search_tasks
                (keyword, region, search_phone_status, search_email_status, created_at, updated_at)
                VALUES {', '.join(values)}
                """

                result = db.execute(text(insert_sql))
                db.commit()
                inserted_count += result.rowcount

                progress = (i + len(batch)) / len(task_combinations)
                logger.info(f"插入进度：{i + len(batch)}/{len(task_combinations)} ({progress:.2%}) - 本批次插入 {result.rowcount} 条")

        logger.info(f"✅ 任务生成完成！总共插入{inserted_count}个搜索任务")

        # 显示统计信息
        phone_pending = db.query(SearchTasks).filter(SearchTasks.search_phone_status == 'pending').count()

        logger.info(f"📊 统计信息:")
        logger.info(f"   - 待处理电话搜索任务: {phone_pending}")

    except Exception as e:
        logger.error(f"生成任务失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def check_existing_tasks():
    """检查现有任务数量"""
    db = get_db_session()
    try:
        total_tasks = db.query(SearchTasks).count()
        phone_pending = db.query(SearchTasks).filter(SearchTasks.search_phone_status == 'pending').count()
        phone_completed = db.query(SearchTasks).filter(SearchTasks.search_phone_status == 'completed').count()

        logger.info(f"📊 现有任务统计:")
        logger.info(f"   - 总任务数: {total_tasks}")
        logger.info(f"   - 电话搜索待处理: {phone_pending}")
        logger.info(f"   - 电话搜索已完成: {phone_completed}")

        return total_tasks
    except Exception as e:
        logger.error(f"检查现有任务失败: {e}")
        return 0
    finally:
        db.close()


def generate_tasks(keywords, regions):
    """生成所有搜索任务（兼容旧版本）"""
    return generate_tasks_fast(keywords, regions)


def main():
    """主函数"""
    logger.info("🚀 开始生成搜索任务")

    # 检查现有任务
    existing_count = check_existing_tasks()

    # 加载关键词
    keywords = load_keywords()
    if not keywords:
        logger.error("❌ 未能加载关键词，程序退出")
        return

    # 加载地区信息
    regions = load_regions()
    if not regions:
        logger.error("❌ 未能加载地区信息，程序退出")
        return

    # 显示预览信息
    expected_new_tasks = len(keywords) * len(regions)
    logger.info(f"📋 任务预览:")
    logger.info(f"   - 关键词数量: {len(keywords)}")
    logger.info(f"   - 地区数量: {len(regions)}")
    logger.info(f"   - 预计新增任务数: {expected_new_tasks}")
    logger.info(f"   - 现有任务数: {existing_count}")
    logger.info(f"   - 预计总任务数: {existing_count + expected_new_tasks}")

    # 显示示例
    if keywords and regions:
        example_keyword = keywords[0]
        example_region = regions[0]
        logger.info(f"   - 示例任务: {example_keyword} + {example_region}")
        logger.info(f"   - 电话搜索词: {example_keyword} phone {example_region}")

    # 性能提示
    if expected_new_tasks > 10000:
        logger.warning(f"⚠️  任务数量较大({expected_new_tasks})，建议使用快速插入模式")

    # 确认生成
    confirm = input("\n是否继续生成任务？(Y/n): ").strip().lower()
    if confirm in ['', 'y', 'yes']:
        import time
        start_time = time.time()
        generate_tasks(keywords, regions)
        end_time = time.time()
        logger.info(f"⏱️  总耗时: {end_time - start_time:.2f} 秒")
    else:
        logger.info("❌ 用户取消生成任务")


if __name__ == "__main__":
    main()
