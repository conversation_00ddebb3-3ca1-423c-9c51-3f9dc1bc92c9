# -*- coding: utf-8 -*-
"""
Database initialization for phone and email crawler
Author: ji<PERSON> wei
Create Time: 2025/8/18
File Name: init_db.py
"""
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv

from .db_models import Base

load_dotenv()

# 数据库配置
mysql_host = os.getenv('MYSQL_HOST', '************')
mysql_port = os.getenv('MYSQL_PORT', '13306')
mysql_user = os.getenv('MYSQL_USER', 'root')
mysql_password = os.getenv('MYSQL_PASSWORD', 'qwer1234')
mysql_db = os.getenv('MYSQL_DATABASE', 'phone_email_crawler')

# 创建临时引擎检查数据库是否存在
temp_engine = create_engine(f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}")

# 检查数据库是否存在，如果不存在则创建
with temp_engine.connect() as conn:
    conn.execute(text(f"CREATE DATABASE IF NOT EXISTS {mysql_db}"))

# 创建正式的数据库连接
DATABASE_URL = f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_db}"

engine = create_engine(
    DATABASE_URL,
    pool_recycle=1800,
    pool_pre_ping=True,
    pool_size=5,
    max_overflow=10,
    echo=False  # 设置为True可以看到SQL语句
)

# 创建所有表
Base.metadata.create_all(engine)

# 创建Session工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=True, bind=engine, expire_on_commit=False)


def get_db_session():
    """获取数据库会话"""
    return SessionLocal()


if __name__ == "__main__":
    print("数据库初始化完成")
    print(f"数据库URL: {DATABASE_URL}")
    print("已创建的表:")
    for table_name in Base.metadata.tables.keys():
        print(f"  - {table_name}")
