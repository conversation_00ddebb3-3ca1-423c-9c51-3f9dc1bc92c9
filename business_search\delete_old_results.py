#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delete old search results from search_results table
Author: ji<PERSON> wei
Create Time: 2025/9/3
File Name: delete_old_results.py

功能：删除search_results表中id小于60000的数据
特点：分批次删除，使用原生SQL，避免内存溢出
"""
import sys
import os
import time
import logging
from sqlalchemy import text

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from business_search.db.init_db import get_db_session

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('DeleteOldResults')


def count_records_to_delete(max_id):
    """统计需要删除的记录数量"""
    db = get_db_session()
    try:
        sql = f"SELECT COUNT(*) FROM search_results WHERE id < {max_id}"
        result = db.execute(text(sql)).scalar()
        return result
    except Exception as e:
        logger.error(f"统计记录数量失败: {e}")
        return 0
    finally:
        db.close()


def delete_batch(max_id, batch_size=10000):
    """分批删除记录"""
    db = get_db_session()
    try:
        # 使用LIMIT删除指定数量的记录
        sql = f"DELETE FROM search_results WHERE id < {max_id} LIMIT {batch_size}"
        result = db.execute(text(sql))
        deleted_count = result.rowcount
        db.commit()

        logger.info(f"本批次删除了 {deleted_count} 条记录")
        return deleted_count

    except Exception as e:
        logger.error(f"删除批次失败: {e}")
        db.rollback()
        return 0
    finally:
        db.close()


def delete_old_results_batch(max_id, batch_size=10000, delay_seconds=0):
    """分批次删除旧的搜索结果"""

    logger.info("=" * 60)
    logger.info(f"开始删除search_results表中id < {max_id}的记录")
    logger.info("=" * 60)

    # 统计总记录数
    total_records = count_records_to_delete(max_id)
    if total_records == 0:
        logger.info(f"✅ 没有需要删除的记录 (id < {max_id})")
        return

    logger.info(f"📊 删除条件: id < {max_id}")
    logger.info(f"📊 需要删除的记录总数: {total_records:,}")
    logger.info(f"📦 批次大小: {batch_size:,}")
    logger.info(f"⏱️ 批次间隔: {delay_seconds} 秒")

    estimated_batches = (total_records + batch_size - 1) // batch_size
    logger.info(f"📈 预计批次数: {estimated_batches}")

    logger.info("🚀 开始执行删除操作...")
    
    # 开始分批删除
    total_deleted = 0
    batch_count = 0
    start_time = time.time()
    
    logger.info("🚀 开始分批删除...")
    
    while True:
        batch_count += 1
        batch_start_time = time.time()
        
        # 删除一批数据
        deleted_count = delete_batch(max_id, batch_size)
        
        if deleted_count == 0:
            logger.info("✅ 所有符合条件的记录已删除完成")
            break
        
        total_deleted += deleted_count
        batch_duration = time.time() - batch_start_time
        
        # 计算进度
        remaining_records = count_records_to_delete(max_id)
        progress = ((total_records - remaining_records) / total_records) * 100
        
        logger.info(f"📦 批次 {batch_count}: 删除 {deleted_count:,} 条，"
                   f"累计 {total_deleted:,} 条，"
                   f"剩余 {remaining_records:,} 条，"
                   f"进度 {progress:.1f}%，"
                   f"耗时 {batch_duration:.2f}s")
        
        # 如果没有更多记录需要删除，退出循环
        if remaining_records == 0:
            logger.info("✅ 所有符合条件的记录已删除完成")
            break
        
        # 批次间延迟，避免对数据库造成过大压力
        if delay_seconds > 0:
            time.sleep(delay_seconds)
    
    # 删除完成统计
    total_duration = time.time() - start_time
    
    logger.info("=" * 60)
    logger.info("🎉 删除操作完成！")
    logger.info(f"📊 统计信息:")
    logger.info(f"   - 总删除记录: {total_deleted:,}")
    logger.info(f"   - 执行批次数: {batch_count}")
    logger.info(f"   - 总耗时: {total_duration:.2f} 秒")
    if batch_count > 0:
        logger.info(f"   - 平均每批次: {total_deleted/batch_count:.0f} 条")
        logger.info(f"   - 平均每批次耗时: {total_duration/batch_count:.2f} 秒")
    logger.info("=" * 60)
    
    # 最终验证
    final_count = count_records_to_delete(max_id)
    if final_count == 0:
        logger.info(f"✅ 验证通过：所有id < {max_id}的记录已删除")
    else:
        logger.warning(f"⚠️ 验证发现：仍有 {final_count:,} 条记录未删除")


def main():
    """主函数"""
    # 设置要删除的最大ID（删除小于此ID的所有记录）
    MAX_ID_TO_DELETE = 1232064  # 可以修改这个值来删除不同范围的数据

    print("🗑️ search_results表数据清理工具")
    print(f"删除条件: id < {MAX_ID_TO_DELETE}")
    print("删除方式: 分批次原生SQL删除")
    print()

    try:
        # 执行删除，每批10000条，间隔1秒
        delete_old_results_batch(
            max_id=MAX_ID_TO_DELETE,
            batch_size=10000,
            delay_seconds=1
        )

    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断删除操作")
    except Exception as e:
        logger.error(f"❌ 删除操作出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
